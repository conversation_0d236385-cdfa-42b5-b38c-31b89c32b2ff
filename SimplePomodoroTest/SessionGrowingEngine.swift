import Foundation

/// Система взращивания сессий
/// Предлагает дополнительные интервалы после завершения основного, используя психологический момент успеха
class SessionGrowingEngine {
    static let shared = SessionGrowingEngine()
    
    // Текущая сессия
    private var currentSession: GrowingSession?
    
    // Колбэки
    var onGrowthOffered: ((Int, @escaping (Bool) -> Void) -> Void)? // (минуты, колбэк с ответом)
    var onSessionCompleted: ((GrowingSession) -> Void)?
    
    private init() {}
    
    // MARK: - Основные методы
    
    /// Начинает новую сессию взращивания
    /// - Parameter initialDuration: Начальная длительность в секундах
    func startSession(initialDuration: TimeInterval) {
        let session = GrowingSession(initialDuration: initialDuration)
        currentSession = session
        
        logInfo("SessionGrowing", "🌱 Начата сессия взращивания: \(Int(initialDuration/60)) мин")
    }
    
    /// Добавляет сегмент роста к текущей сессии
    /// - Parameter duration: Длительность сегмента в секундах
    func addGrowthSegment(duration: TimeInterval) {
        guard let session = currentSession else {
            logWarning("SessionGrowing", "Попытка добавить сегмент без активной сессии")
            return
        }
        
        session.addSegment(duration: duration)
        logInfo("SessionGrowing", "🌱 Добавлен сегмент: \(Int(duration/60)) мин (всего: \(Int(session.totalDuration/60)) мин)")
    }
    
    /// Завершает текущую сессию и возвращает новую планку
    /// - Parameter oldBar: Старая планка пользователя в секундах
    /// - Returns: Новая планка в секундах
    func finishSession(oldBar: TimeInterval) -> TimeInterval {
        guard let session = currentSession else {
            logWarning("SessionGrowing", "Попытка завершить сессию без активной сессии")
            return oldBar
        }
        
        // Применяем формулу умеренного роста
        let newBar = calculateModerateGrowth(totalCompleted: session.totalDuration, oldBar: oldBar)
        
        logInfo("SessionGrowing", "🌱 Сессия завершена: \(Int(session.totalDuration/60)) мин → планка \(Int(oldBar/60))→\(Int(newBar/60)) мин")
        
        // Уведомляем о завершении
        onSessionCompleted?(session)
        
        // Очищаем текущую сессию
        currentSession = nil
        
        return newBar
    }
    
    /// Проверяет, можно ли предложить взращивание для данного интервала
    /// - Parameter duration: Длительность интервала в секундах
    /// - Returns: true, если можно предложить взращивание
    func canOfferGrowth(for duration: TimeInterval) -> Bool {
        let minutes = Int(duration / 60)
        return minutes < 52 // Взращивание только для интервалов < 52 мин
    }
    
    /// Предлагает взращивание после завершения интервала
    /// - Parameter completedDuration: Завершенная длительность в секундах
    func offerGrowth(for completedDuration: TimeInterval) {
        guard canOfferGrowth(for: completedDuration) else {
            logInfo("SessionGrowing", "🌱 Взращивание не предлагается для \(Int(completedDuration/60)) мин (≥52)")
            return
        }
        
        // Рассчитываем предлагаемое время роста
        let growthMinutes = calculateGrowthOffer(for: completedDuration)
        
        logInfo("SessionGrowing", "🌱 Предлагаем взращивание: +\(growthMinutes) мин к \(Int(completedDuration/60)) мин")
        
        // Предлагаем пользователю
        onGrowthOffered?(growthMinutes) { [weak self] accepted in
            if accepted {
                self?.handleGrowthAccepted(growthMinutes: growthMinutes, originalDuration: completedDuration)
            } else {
                self?.handleGrowthDeclined(originalDuration: completedDuration)
            }
        }
    }
    
    // MARK: - Приватные методы
    
    /// Рассчитывает предлагаемое время роста
    /// - Parameter duration: Завершенная длительность в секундах
    /// - Returns: Предлагаемые минуты роста
    private func calculateGrowthOffer(for duration: TimeInterval) -> Int {
        let minutes = Int(duration / 60)
        
        // Используем градационную систему для расчета роста
        let grownMinutes = GradualGrowthSystem.calculateGrowth(currentBarMinutes: minutes)
        let growthMinutes = grownMinutes - minutes
        
        // Ограничиваем рост максимумом 20 минут за раз
        return min(growthMinutes, 20)
    }
    
    /// Обрабатывает принятие предложения роста
    private func handleGrowthAccepted(growthMinutes: Int, originalDuration: TimeInterval) {
        // Если сессия еще не начата, начинаем ее
        if currentSession == nil {
            startSession(initialDuration: originalDuration)
        }
        
        // Добавляем сегмент роста
        let growthDuration = TimeInterval(growthMinutes * 60)
        addGrowthSegment(duration: growthDuration)
        
        logInfo("SessionGrowing", "🌱 Рост принят: +\(growthMinutes) мин")
    }
    
    /// Обрабатывает отклонение предложения роста
    private func handleGrowthDeclined(originalDuration: TimeInterval) {
        logInfo("SessionGrowing", "🌱 Рост отклонен для \(Int(originalDuration/60)) мин")
        
        // Если была активная сессия, завершаем ее
        if currentSession != nil {
            // Завершаем без обновления планки (пользователь отказался)
            currentSession = nil
        }
    }
    
    /// Применяет формулу умеренного роста
    /// - Parameters:
    ///   - totalCompleted: Общее выполненное время в секундах
    ///   - oldBar: Старая планка в секундах
    /// - Returns: Новая планка в секундах
    private func calculateModerateGrowth(totalCompleted: TimeInterval, oldBar: TimeInterval) -> TimeInterval {
        // Формула: новая_планка = min(общее_выполненное × 0.6, старая_планка × 3)
        let option1 = totalCompleted * 0.6
        let option2 = oldBar * 3.0
        let newBar = min(option1, option2)
        
        // Ограничиваем максимумом 52 минуты
        return min(newBar, 52 * 60)
    }
    
    // MARK: - Отладочные методы
    
    /// Возвращает информацию о текущей сессии
    func getCurrentSessionInfo() -> String {
        guard let session = currentSession else {
            return "Нет активной сессии"
        }
        
        return """
        🌱 Активная сессия взращивания:
        • Начальная длительность: \(Int(session.initialDuration/60)) мин
        • Сегментов роста: \(session.segments.count)
        • Общая длительность: \(Int(session.totalDuration/60)) мин
        • Сегменты: \(session.segments.map { "\(Int($0/60))мин" }.joined(separator: ", "))
        """
    }
    
    /// Сбрасывает текущую сессию (для отладки)
    func resetCurrentSession() {
        currentSession = nil
        logInfo("SessionGrowing", "🌱 Сессия сброшена")
    }
}

// MARK: - Модель сессии

/// Модель растущей сессии
class GrowingSession {
    let initialDuration: TimeInterval
    private(set) var segments: [TimeInterval] = []
    
    var totalDuration: TimeInterval {
        return initialDuration + segments.reduce(0, +)
    }
    
    init(initialDuration: TimeInterval) {
        self.initialDuration = initialDuration
    }
    
    func addSegment(duration: TimeInterval) {
        segments.append(duration)
    }
}

// MARK: - Интеграция с Logger

extension SessionGrowingEngine {
    private func logInfo(_ category: String, _ message: String) {
        Logger.shared.log(.info, category, message)
    }
    
    private func logWarning(_ category: String, _ message: String) {
        Logger.shared.log(.warning, category, message)
    }
}
