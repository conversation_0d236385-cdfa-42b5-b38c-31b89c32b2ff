import Cocoa
import Foundation

/// ОКНО ОТЛАДКИ СИСТЕМЫ РАННЕГО ВОВЛЕЧЕНИЯ
/// Позволяет эмулировать события, просматривать состояние и тестировать систему
class EarlyEngagementDebugWindow: NSWindow {
    
    // Убираем блок состояния - он не нужен

    // Матричный интерфейс
    private var daysPopupButton: NSPopUpButton!
    private var messageNumberPopup: NSPopUpButton!
    private var barPopupButton: NSPopUpButton!  // Новый выпадающий список для планки
    private var showMatrixMessageButton: NSButton!
    private var currentMessageLabel: NSTextField!

    // Система взращивания сессий
    private var sessionGrowingLabel: NSTextField!
    private var testGrowthButton: NSButton!

    // Система полной сессии
    private var fullSessionLabel: NSTextField!
    private var testFullSessionButton: NSButton!

    // Упрощенный режим отладки (без тестового режима)
    
    override init(contentRect: NSRect, styleMask style: NSWindow.StyleMask, backing backingStoreType: NSWindow.BackingStoreType, defer flag: Bool) {
        super.init(contentRect: NSRect(x: 100, y: 100, width: 800, height: 500),
                  styleMask: [.titled, .closable, .resizable],
                  backing: .buffered,
                  defer: false)
        
        setupWindow()
        setupUI()
    }
    
    private func setupWindow() {
        title = "🌅 Отладка системы раннего вовлечения"
        center()
        isReleasedWhenClosed = false
        
        print("🔧 Открыто окно отладки системы раннего вовлечения")
    }
    
    private func setupUI() {
        let contentView = NSView(frame: self.contentView!.bounds)
        contentView.autoresizingMask = [.width, .height]
        self.contentView = contentView

        var yPos: CGFloat = contentView.bounds.height - 30
        let margin: CGFloat = 20
        let buttonHeight: CGFloat = 30
        let spacing: CGFloat = 15

        // Заголовок
        let titleLabel = NSTextField(labelWithString: "🔧 ОТЛАДКА МАТРИЦЫ СООБЩЕНИЙ")
        titleLabel.font = NSFont.boldSystemFont(ofSize: 16)
        titleLabel.frame = NSRect(x: margin, y: yPos, width: contentView.bounds.width - 2*margin, height: 25)
        titleLabel.autoresizingMask = [.width]
        contentView.addSubview(titleLabel)
        yPos -= 40

        // === ТЕСТИРОВАНИЕ МАТРИЦЫ ===

        // 1. Выбор изначальной планки (ПЕРВОЕ ПОЛЕ)
        let barLabel = NSTextField(labelWithString: "Изначальная планка (минуты):")
        barLabel.frame = NSRect(x: margin, y: yPos, width: 200, height: 20)
        contentView.addSubview(barLabel)
        yPos -= 25

        barPopupButton = NSPopUpButton(frame: NSRect(x: margin, y: yPos, width: 150, height: 25))
        // Добавляем варианты планки от 3 до 52 минут
        let barOptions = [3, 5, 8, 10, 15, 20, 25, 30, 35, 40, 45, 50, 52]
        for minutes in barOptions {
            barPopupButton.addItem(withTitle: "\(minutes) мин")
        }
        barPopupButton.selectItem(at: 0) // По умолчанию 3 минуты
        barPopupButton.target = self
        barPopupButton.action = #selector(matrixSelectionChanged)
        contentView.addSubview(barPopupButton)
        yPos -= 40

        // 2. Контейнер в два столбца
        let containerHeight: CGFloat = 80
        let leftColumnWidth: CGFloat = 250
        let rightColumnWidth: CGFloat = 300

        // Левый столбец - Вертикаль (дни без работы)
        let daysLabel = NSTextField(labelWithString: "Вертикаль (дни без работы):")
        daysLabel.frame = NSRect(x: margin, y: yPos, width: leftColumnWidth, height: 20)
        contentView.addSubview(daysLabel)

        daysPopupButton = NSPopUpButton(frame: NSRect(x: margin, y: yPos - 25, width: leftColumnWidth, height: 25))
        daysPopupButton.addItems(withTitles: [
            "0 дней (вчера работал)",
            "1 день не работал",
            "2-3 дня не работал",
            "4-6 дней не работал",
            "7+ дней не работал"
        ])
        daysPopupButton.target = self
        daysPopupButton.action = #selector(matrixSelectionChanged)
        contentView.addSubview(daysPopupButton)

        // Правый столбец - Горизонталь (номер сообщения)
        let messageLabel = NSTextField(labelWithString: "Горизонталь (номер сообщения):")
        messageLabel.frame = NSRect(x: margin + leftColumnWidth + 20, y: yPos, width: rightColumnWidth, height: 20)
        contentView.addSubview(messageLabel)

        messageNumberPopup = NSPopUpButton(frame: NSRect(x: margin + leftColumnWidth + 20, y: yPos - 25, width: rightColumnWidth, height: 25))
        messageNumberPopup.addItems(withTitles: [
            "1-е сообщение (сразу при пробуждении)",
            "2-е сообщение (через 20 минут)",
            "3-е сообщение (через 1 час)",
            "4-е сообщение (через 2 часа или в 14:00)"
        ])
        messageNumberPopup.target = self
        messageNumberPopup.action = #selector(matrixSelectionChanged)
        contentView.addSubview(messageNumberPopup)
        yPos -= containerHeight

        // 3. Кнопка показа окна для выбранной позиции
        showMatrixMessageButton = NSButton(title: "📢 ПОКАЗАТЬ ОКНО", target: self, action: #selector(showMatrixMessage))
        showMatrixMessageButton.frame = NSRect(x: margin, y: yPos, width: 200, height: buttonHeight)
        showMatrixMessageButton.bezelStyle = .rounded
        contentView.addSubview(showMatrixMessageButton)
        yPos -= buttonHeight + spacing

        // 4. Отладочная информация о сообщении
        let currentMessageTitleLabel = NSTextField(labelWithString: "💬 Отладка матрицы сообщений:")
        currentMessageTitleLabel.font = NSFont.boldSystemFont(ofSize: 12)
        currentMessageTitleLabel.frame = NSRect(x: margin, y: yPos, width: 300, height: 20)
        contentView.addSubview(currentMessageTitleLabel)
        yPos -= 25

        currentMessageLabel = NSTextField(labelWithString: "Выберите позицию в матрице")
        currentMessageLabel.frame = NSRect(x: margin, y: yPos - 120, width: contentView.bounds.width - 2*margin, height: 140)
        currentMessageLabel.autoresizingMask = [.width]
        currentMessageLabel.maximumNumberOfLines = 0 // Без ограничений
        currentMessageLabel.cell?.wraps = true
        currentMessageLabel.font = NSFont.systemFont(ofSize: 11)
        currentMessageLabel.isSelectable = true // Разрешаем выделение текста
        currentMessageLabel.isEditable = false // Но запрещаем редактирование
        contentView.addSubview(currentMessageLabel)

        // Обновляем информацию при инициализации
        updateMatrixMessage()

        // === СИСТЕМА ВЗРАЩИВАНИЯ СЕССИЙ ===
        yPos -= 180 // Отступ от предыдущей секции

        let sessionGrowingTitleLabel = NSTextField(labelWithString: "🌱 СИСТЕМА ВЗРАЩИВАНИЯ СЕССИЙ")
        sessionGrowingTitleLabel.font = NSFont.boldSystemFont(ofSize: 14)
        sessionGrowingTitleLabel.frame = NSRect(x: margin, y: yPos, width: 400, height: 20)
        contentView.addSubview(sessionGrowingTitleLabel)
        yPos -= 30

        // Информация о текущей сессии
        sessionGrowingLabel = NSTextField(labelWithString: "Нет активной сессии")
        sessionGrowingLabel.frame = NSRect(x: margin, y: yPos - 80, width: contentView.bounds.width - 2*margin, height: 100)
        sessionGrowingLabel.autoresizingMask = [.width]
        sessionGrowingLabel.maximumNumberOfLines = 0
        sessionGrowingLabel.cell?.wraps = true
        sessionGrowingLabel.font = NSFont.systemFont(ofSize: 11)
        sessionGrowingLabel.isSelectable = true // Разрешаем выделение текста
        sessionGrowingLabel.isEditable = false // Но запрещаем редактирование
        contentView.addSubview(sessionGrowingLabel)
        yPos -= 90

        // Кнопка тестирования взращивания
        testGrowthButton = NSButton(title: "🧪 Тест взращивания (15 мин)", target: self, action: #selector(testSessionGrowth))
        testGrowthButton.frame = NSRect(x: margin, y: yPos, width: 250, height: buttonHeight)
        testGrowthButton.bezelStyle = .rounded
        contentView.addSubview(testGrowthButton)

        // Обновляем информацию о взращивании
        updateSessionGrowingInfo()

        // === СИСТЕМА ПОЛНОЙ СЕССИИ ===
        yPos -= 50 // Отступ от предыдущей секции

        let fullSessionTitleLabel = NSTextField(labelWithString: "🌟 СИСТЕМА ПОЛНОЙ СЕССИИ")
        fullSessionTitleLabel.font = NSFont.boldSystemFont(ofSize: 14)
        fullSessionTitleLabel.frame = NSRect(x: margin, y: yPos, width: 400, height: 20)
        contentView.addSubview(fullSessionTitleLabel)
        yPos -= 30

        // Информация о системе полной сессии
        fullSessionLabel = NSTextField(labelWithString: "Загрузка...")
        fullSessionLabel.frame = NSRect(x: margin, y: yPos - 80, width: contentView.bounds.width - 2*margin, height: 100)
        fullSessionLabel.autoresizingMask = [.width]
        fullSessionLabel.maximumNumberOfLines = 0
        fullSessionLabel.cell?.wraps = true
        fullSessionLabel.font = NSFont.systemFont(ofSize: 11)
        fullSessionLabel.isSelectable = true // Разрешаем выделение текста
        fullSessionLabel.isEditable = false // Но запрещаем редактирование
        contentView.addSubview(fullSessionLabel)
        yPos -= 90

        // Кнопка тестирования полной сессии
        testFullSessionButton = NSButton(title: "🧪 Тест полной сессии", target: self, action: #selector(testFullSession))
        testFullSessionButton.frame = NSRect(x: margin, y: yPos, width: 200, height: buttonHeight)
        testFullSessionButton.bezelStyle = .rounded
        contentView.addSubview(testFullSessionButton)

        // Обновляем информацию о полной сессии
        updateFullSessionInfo()

        // Запускаем таймер для обновления информации о взращивании и полной сессии
        Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateSessionGrowingInfo()
            self?.updateFullSessionInfo()
        }
    }
    
    @objc private func matrixSelectionChanged() {
        updateMatrixMessage()
    }



    private func updateMatrixMessage() {
        let daysLevel = daysPopupButton.indexOfSelectedItem
        let messageIndex = messageNumberPopup.indexOfSelectedItem

        // Получаем изначальную планку из выпадающего списка
        let barOptions = [3, 5, 8, 10, 15, 20, 25, 30, 35, 40, 45, 50, 52]
        let selectedBarIndex = barPopupButton.indexOfSelectedItem
        let initialBarMinutes = barOptions[selectedBarIndex]

        // Используем реальную логику из EarlyEngagementSystem
        let calculatedBarMinutes = EarlyEngagementSystem.shared.debugCalculateBarForScenario(
            initialBar: initialBarMinutes,
            daysWithoutWork: daysLevel,
            messageIndex: messageIndex
        )

        // Получаем сообщение из матрицы
        let message = MessageConstructionMatrix.getMessage(vertical: daysLevel, horizontal: messageIndex)

        // Подставляем рассчитанные значения в сообщение
        let processedMessage = substituteVariablesInMessage(message, calculatedBarMinutes: calculatedBarMinutes)

        // Обновляем отображение
        let daysLabels = ["0 дней (вчера работал)", "1 день не работал", "2-3 дня не работал", "4-6 дней не работал", "7+ дней не работал"]
        let timeLabels = [
            "1-е сообщение (сразу при пробуждении)",
            "2-е сообщение (через 20 минут)",
            "3-е сообщение (через 1 час)",
            "4-е сообщение (через 2 часа или в 14:00)"
        ]

        // Получаем информацию о приоритетном проекте
        let projectManager = ProjectManager()
        let priorityProjectName = projectManager.getPriorityProject()?.name ?? "НЕ ВЫБРАН"

        // Используем реальную формулу из EarlyEngagementSystem
        let formulaText = EarlyEngagementSystem.shared.debugGetFormulaText(
            daysLevel: daysLevel,
            messageIndex: messageIndex,
            initialBar: initialBarMinutes,
            calculatedBar: calculatedBarMinutes
        )

        currentMessageLabel.stringValue = """
        📍 Позиция: \(daysLabels[daysLevel]) × \(timeLabels[messageIndex])
        🎯 Приоритетный проект: \(priorityProjectName)

        ⏱️ Изначальная планка: \(initialBarMinutes) мин
        🧮 Расчет: \(formulaText)
        ✅ Рассчитанная планка: \(calculatedBarMinutes) мин

        📝 Заголовок: "\(processedMessage.title)"
        📄 Текст: "\(processedMessage.subtitle)"
        🔘 Кнопки: "\(processedMessage.buttonText)" | "Не сейчас" | "Через 30 мин"
        """
    }

    // Методы расчета теперь находятся в EarlyEngagementSystem для синхронизации

    private func substituteVariablesInMessage(_ message: EngagementMessage, calculatedBarMinutes: Int) -> EngagementMessage {
        let projectManager = ProjectManager()
        let priorityProject = projectManager.getPriorityProject()
        let projectName = priorityProject?.name ?? "приоритетный проект"

        var processedMessage = message

        // Подставляем проект (используем правильные переменные из MessageConstructionMatrix)
        processedMessage.title = processedMessage.title.replacingOccurrences(of: "[focused_project]", with: projectName)
        processedMessage.subtitle = processedMessage.subtitle.replacingOccurrences(of: "[focused_project]", with: projectName)
        processedMessage.buttonText = processedMessage.buttonText.replacingOccurrences(of: "[focused_project]", with: projectName)

        // Подставляем время (используем правильные переменные из MessageConstructionMatrix)
        processedMessage.title = processedMessage.title.replacingOccurrences(of: "[current_bar]", with: "\(calculatedBarMinutes)")
        processedMessage.subtitle = processedMessage.subtitle.replacingOccurrences(of: "[current_bar]", with: "\(calculatedBarMinutes)")
        processedMessage.buttonText = processedMessage.buttonText.replacingOccurrences(of: "[current_bar]", with: "\(calculatedBarMinutes)")

        // Обновляем предлагаемую длительность
        processedMessage.proposedDuration = TimeInterval(calculatedBarMinutes * 60)

        return processedMessage
    }

    @objc private func showMatrixMessage() {
        let daysLevel = daysPopupButton.indexOfSelectedItem
        let messageIndex = messageNumberPopup.indexOfSelectedItem

        // Получаем изначальную планку из выпадающего списка
        let barOptions = [3, 5, 8, 10, 15, 20, 25, 30, 35, 40, 45, 50, 52]
        let selectedBarIndex = barPopupButton.indexOfSelectedItem
        let initialBarMinutes = barOptions[selectedBarIndex]

        // Используем реальную логику из EarlyEngagementSystem
        let calculatedBarMinutes = EarlyEngagementSystem.shared.debugCalculateBarForScenario(
            initialBar: initialBarMinutes,
            daysWithoutWork: daysLevel,
            messageIndex: messageIndex
        )

        print("🎯 Показ окна сообщения матрицы: дни=\(daysLevel), сообщение=\(messageIndex), изначальная планка=\(initialBarMinutes)мин, рассчитанная=\(calculatedBarMinutes)мин")

        // Получаем сообщение из матрицы
        let message = MessageConstructionMatrix.getMessage(vertical: daysLevel, horizontal: messageIndex)

        // Подставляем переменные в сообщение
        let processedMessage = substituteVariablesInMessage(message, calculatedBarMinutes: calculatedBarMinutes)

        // Показываем реальное окно сообщения с обработанным сообщением
        let engagementWindow = EarlyEngagementWindow()
        engagementWindow.showEngagementMessage(
            processedMessage,
            onAccept: { projectId in
                print("✅ Пользователь принял: \(projectId?.uuidString ?? "без проекта")")
            },
            onDecline: {
                print("❌ Пользователь отклонил")
            },
            onSnooze: {
                print("⏰ Пользователь отложил на 30 минут")
            },
            onFullSession: { projectId in
                print("🌟 Пользователь выбрал полную сессию: \(projectId?.uuidString ?? "без проекта")")
            }
        )

        print("✅ Показано окно сообщения: \(message.title)")
    }

    // MARK: - Session Growing System Methods

    /// Обновляет информацию о системе взращивания сессий
    private func updateSessionGrowingInfo() {
        let info = SessionGrowingEngine.shared.getCurrentSessionInfo()
        sessionGrowingLabel.stringValue = info
    }

    /// Тестирует систему взращивания сессий
    @objc private func testSessionGrowth() {
        print("🧪 EarlyEngagementDebugWindow: Тестирование системы взращивания")

        // Симулируем предложение взращивания на 15 минут
        SessionGrowingEngine.shared.offerGrowth(for: 15 * 60) // 15 минут в секундах
    }

    // MARK: - Full Session System Methods

    /// Обновляет информацию о системе полной сессии
    private func updateFullSessionInfo() {
        let stats = FullSessionSystem.shared.getCurrentStatistics()
        let currentBar = EarlyEngagementSystem.shared.getCurrentUserBar()
        let shouldShow = FullSessionSystem.shared.shouldShowFullSession(currentBar: currentBar)

        let info = """
        \(stats)

        🎯 Текущее состояние:
        • Планка пользователя: \(Int(currentBar / 60)) мин
        • Показывать кнопку: \(shouldShow ? "ДА" : "НЕТ")
        """

        fullSessionLabel.stringValue = info
    }

    /// Тестирует систему полной сессии
    @objc private func testFullSession() {
        print("🧪 EarlyEngagementDebugWindow: Тестирование системы полной сессии")

        // Регистрируем попытку полной сессии
        FullSessionSystem.shared.recordFullSessionAttempt()

        // Обновляем информацию
        updateFullSessionInfo()

        print("✅ Зарегистрирована попытка полной сессии")
    }
}


