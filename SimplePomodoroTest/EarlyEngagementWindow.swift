import Cocoa

/// Окно для показа сообщений системы раннего вовлечения
/// Наследует от IntervalNotificationWindow и адаптирует UI для специфических нужд
class EarlyEngagementWindow: NSWindow {
    
    // MARK: - Properties
    
    private var engagementMessage: EngagementMessage?
    private var onAccept: ((UUID?) -> Void)?
    private var onDecline: (() -> Void)?
    private var onSnooze: (() -> Void)?
    private var onFullSession: ((UUID?) -> Void)?

    private var titleLabel: NSTextField!
    private var subtitleLabel: NSTextField!
    private var acceptButton: NSButton!
    private var fullSessionButton: NSButton!
    private var declineButton: NSButton!
    private var snoozeButton: NSButton!
    
    // MARK: - Initialization
    
    init() {
        // Создаем окно немного больше чем IntervalNotificationWindow для дополнительного контента
        let windowRect = NSRect(x: 0, y: 0, width: 380, height: 190)
        
        super.init(
            contentRect: windowRect,
            styleMask: [.borderless],
            backing: .buffered,
            defer: false
        )
        
        Logger.shared.log(.info, "EarlyEngagement", "🎨 EarlyEngagementWindow: Инициализация окна раннего вовлечения")
        setupWindow()
        setupUI()
        positionWindow()
    }

    // MARK: - Window Setup

    private func setupWindow() {
        Logger.shared.log(.info, "EarlyEngagement", "🎨 EarlyEngagementWindow: Настройка окна")
        
        // Настройки окна (аналогично IntervalNotificationWindow)
        self.level = .floating
        self.isOpaque = false
        self.backgroundColor = NSColor.clear
        self.ignoresMouseEvents = false
        self.hasShadow = true
        
        // Показываем на всех рабочих столах
        self.collectionBehavior = [.canJoinAllSpaces, .fullScreenAuxiliary]
    }
    
    private func positionWindow() {
        // Позиционируем окно в правом верхнем углу
        if let screen = NSScreen.main {
            let screenFrame = screen.frame
            let windowFrame = self.frame
            
            let x = screenFrame.maxX - windowFrame.width - 10
            let y = screenFrame.maxY - windowFrame.height - 30
            
            self.setFrameOrigin(NSPoint(x: x, y: y))
        }
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        Logger.shared.log(.info, "EarlyEngagement", "🎨 EarlyEngagementWindow: Настройка UI")
        
        let contentView = NSView(frame: self.contentRect(forFrameRect: self.frame))
        self.contentView = contentView
        
        // Visual effect view для glassmorphism размытия
        let visualEffectView = NSVisualEffectView()
        visualEffectView.material = .hudWindow
        visualEffectView.blendingMode = .behindWindow
        visualEffectView.state = .active
        visualEffectView.wantsLayer = true
        visualEffectView.layer?.cornerRadius = 12
        visualEffectView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(visualEffectView)
        
        // Основной контейнер с градиентом
        let containerView = GradientView()
        containerView.translatesAutoresizingMaskIntoConstraints = false
        visualEffectView.addSubview(containerView)
        
        // Иконка раннего вовлечения (восход солнца)
        let iconView = createSunriseIcon()
        iconView.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(iconView)
        
        // Заголовок
        titleLabel = NSTextField(labelWithString: "Время для работы!")
        titleLabel.font = NSFont.systemFont(ofSize: 14, weight: .semibold)
        titleLabel.textColor = NSColor.white
        titleLabel.alignment = .left
        titleLabel.maximumNumberOfLines = 1
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(titleLabel)
        
        // Подзаголовок
        subtitleLabel = NSTextField(labelWithString: "Начнем с небольшого интервала?")
        subtitleLabel.font = NSFont.systemFont(ofSize: 11, weight: .medium)
        subtitleLabel.textColor = NSColor.white.withAlphaComponent(0.9)
        subtitleLabel.alignment = .left
        subtitleLabel.maximumNumberOfLines = 2
        subtitleLabel.preferredMaxLayoutWidth = 250
        subtitleLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(subtitleLabel)
        
        // Кнопки
        acceptButton = createStyledButton(title: "Начать работу", isGreen: true)
        acceptButton.target = self
        acceptButton.action = #selector(acceptButtonClicked)

        fullSessionButton = createStyledButton(title: "Полная сессия (52 мин)", isGreen: true)
        fullSessionButton.target = self
        fullSessionButton.action = #selector(fullSessionButtonClicked)
        fullSessionButton.isHidden = true // По умолчанию скрыта

        declineButton = createStyledButton(title: "Не сейчас", isGreen: false)
        declineButton.target = self
        declineButton.action = #selector(declineButtonClicked)

        snoozeButton = createStyledButton(title: "Через 30 мин", isGreen: false)
        snoozeButton.target = self
        snoozeButton.action = #selector(snoozeButtonClicked)

        containerView.addSubview(acceptButton)
        containerView.addSubview(fullSessionButton)
        containerView.addSubview(declineButton)
        containerView.addSubview(snoozeButton)
        
        // Constraints
        NSLayoutConstraint.activate([
            // Visual effect view заполняет весь contentView
            visualEffectView.topAnchor.constraint(equalTo: contentView.topAnchor),
            visualEffectView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            visualEffectView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            visualEffectView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            
            // Container view заполняет visual effect view
            containerView.topAnchor.constraint(equalTo: visualEffectView.topAnchor),
            containerView.leadingAnchor.constraint(equalTo: visualEffectView.leadingAnchor),
            containerView.trailingAnchor.constraint(equalTo: visualEffectView.trailingAnchor),
            containerView.bottomAnchor.constraint(equalTo: visualEffectView.bottomAnchor),
            
            // Иконка в левом верхнем углу
            iconView.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 15),
            iconView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 15),
            iconView.widthAnchor.constraint(equalToConstant: 32),
            iconView.heightAnchor.constraint(equalToConstant: 32),
            
            // Заголовок рядом с иконкой
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 15),
            titleLabel.leadingAnchor.constraint(equalTo: iconView.trailingAnchor, constant: 12),
            titleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -15),
            
            // Подзаголовок под заголовком
            subtitleLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 4),
            subtitleLabel.leadingAnchor.constraint(equalTo: titleLabel.leadingAnchor),
            subtitleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -15),
            
            // Первый ряд кнопок (основные действия)
            acceptButton.topAnchor.constraint(equalTo: subtitleLabel.bottomAnchor, constant: 20),
            acceptButton.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 15),
            acceptButton.widthAnchor.constraint(equalToConstant: 110),
            acceptButton.heightAnchor.constraint(equalToConstant: 28),

            // Кнопка полной сессии рядом с основной
            fullSessionButton.topAnchor.constraint(equalTo: acceptButton.topAnchor),
            fullSessionButton.leadingAnchor.constraint(equalTo: acceptButton.trailingAnchor, constant: 8),
            fullSessionButton.widthAnchor.constraint(equalToConstant: 140),
            fullSessionButton.heightAnchor.constraint(equalToConstant: 28),

            // Второй ряд кнопок (дополнительные действия)
            declineButton.topAnchor.constraint(equalTo: acceptButton.bottomAnchor, constant: 8),
            declineButton.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 15),
            declineButton.widthAnchor.constraint(equalToConstant: 80),
            declineButton.heightAnchor.constraint(equalToConstant: 28),
            declineButton.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -15),

            // Кнопка "Через 30 мин" рядом с "Не сейчас"
            snoozeButton.topAnchor.constraint(equalTo: declineButton.topAnchor),
            snoozeButton.leadingAnchor.constraint(equalTo: declineButton.trailingAnchor, constant: 8),
            snoozeButton.widthAnchor.constraint(equalToConstant: 100),
            snoozeButton.heightAnchor.constraint(equalToConstant: 28)
        ])
        
        Logger.shared.log(.info, "EarlyEngagement", "🎨 EarlyEngagementWindow: UI настроен")
    }
    
    // MARK: - UI Creation Helpers
    
    private func createStyledButton(title: String, isGreen: Bool) -> NSButton {
        let button = NSButton(title: title, target: nil, action: nil)
        button.wantsLayer = true
        button.font = NSFont.systemFont(ofSize: 11, weight: .medium)
        button.translatesAutoresizingMaskIntoConstraints = false
        
        if isGreen {
            // Зеленая кнопка "Начать работу"
            button.bezelStyle = .rounded
            button.contentTintColor = NSColor.systemGreen
            button.layer?.backgroundColor = NSColor.systemGreen.withAlphaComponent(0.15).cgColor
            button.layer?.borderColor = NSColor.systemGreen.cgColor
            button.layer?.borderWidth = 1.0
            button.layer?.cornerRadius = 6.0
        } else {
            // Серые кнопки
            button.bezelStyle = .rounded
            button.contentTintColor = NSColor.controlAccentColor
            button.layer?.backgroundColor = NSColor.controlAccentColor.withAlphaComponent(0.1).cgColor
            button.layer?.borderColor = NSColor.controlAccentColor.cgColor
            button.layer?.borderWidth = 1.0
            button.layer?.cornerRadius = 6.0
        }
        
        return button
    }
    
    private func createSunriseIcon() -> NSView {
        let view = NSView()
        view.wantsLayer = true
        
        // Создаем иконку восхода солнца
        let iconLayer = CAShapeLayer()
        let iconPath = NSBezierPath()
        
        // Солнце (круг)
        let sunRect = NSRect(x: 8, y: 16, width: 16, height: 16)
        iconPath.appendOval(in: sunRect)
        
        // Лучи солнца
        let rayLength: CGFloat = 6
        let center = NSPoint(x: 16, y: 24)
        
        // Верхний луч
        iconPath.move(to: NSPoint(x: center.x, y: center.y + 8))
        iconPath.line(to: NSPoint(x: center.x, y: center.y + 8 + rayLength))
        
        // Правый луч
        iconPath.move(to: NSPoint(x: center.x + 8, y: center.y))
        iconPath.line(to: NSPoint(x: center.x + 8 + rayLength, y: center.y))
        
        // Левый луч
        iconPath.move(to: NSPoint(x: center.x - 8, y: center.y))
        iconPath.line(to: NSPoint(x: center.x - 8 - rayLength, y: center.y))
        
        // Горизонт (линия)
        iconPath.move(to: NSPoint(x: 2, y: 8))
        iconPath.line(to: NSPoint(x: 30, y: 8))
        
        iconLayer.path = iconPath.cgPath
        iconLayer.fillColor = NSColor.clear.cgColor
        iconLayer.strokeColor = NSColor.systemOrange.cgColor
        iconLayer.lineWidth = 2
        iconLayer.lineCap = .round
        iconLayer.lineJoin = .round
        
        view.layer?.addSublayer(iconLayer)
        return view
    }
    
    // MARK: - Public Methods
    
    /// Показывает окно с сообщением раннего вовлечения
    func showEngagementMessage(_ message: EngagementMessage,
                              onAccept: @escaping (UUID?) -> Void,
                              onDecline: @escaping () -> Void,
                              onSnooze: @escaping () -> Void,
                              onFullSession: @escaping (UUID?) -> Void) {

        self.engagementMessage = message
        self.onAccept = onAccept
        self.onDecline = onDecline
        self.onSnooze = onSnooze
        self.onFullSession = onFullSession

        // Обновляем текст
        titleLabel.stringValue = message.title
        subtitleLabel.stringValue = message.subtitle
        acceptButton.title = message.buttonText

        // Управляем видимостью кнопки полной сессии
        fullSessionButton.isHidden = !message.showFullSessionButton
        fullSessionButton.title = message.fullSessionButtonText

        Logger.shared.log(.info, "EarlyEngagement", "🎨 Показ сообщения: \(message.title), полная сессия: \(message.showFullSessionButton)")
        showWindow()
    }

    private func showWindow() {
        Logger.shared.log(.info, "EarlyEngagement", "🎨 EarlyEngagementWindow: Показ окна с анимацией")
        
        // Начинаем с прозрачности
        self.alphaValue = 0
        self.makeKeyAndOrderFront(nil)
        
        // Анимация появления
        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.4
            context.timingFunction = CAMediaTimingFunction(name: .easeOut)
            self.animator().alphaValue = 1
        })
        
        NSApp.activate(ignoringOtherApps: true)
    }
    
    private func hideWindow() {
        Logger.shared.log(.info, "EarlyEngagement", "🎨 EarlyEngagementWindow: Скрытие окна с анимацией")
        
        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.3
            context.timingFunction = CAMediaTimingFunction(name: .easeIn)
            self.animator().alphaValue = 0
            
            if let contentView = self.contentView {
                contentView.wantsLayer = true
                let transform = CATransform3DMakeScale(0.9, 0.9, 1)
                contentView.layer?.transform = transform
            }
        }) {
            self.orderOut(nil)
            
            // Возвращаем исходное состояние
            if let contentView = self.contentView {
                contentView.layer?.transform = CATransform3DIdentity
            }
            self.alphaValue = 1
        }
    }
    
    // MARK: - Button Actions
    
    @objc private func acceptButtonClicked() {
        Logger.shared.log(.info, "EarlyEngagement", "✅ Пользователь принял предложение раннего вовлечения")
        hideWindow()

        // Получаем ID проекта если есть
        let projectId = EarlyEngagementSystem.shared.getFocusedProject()?.id
        onAccept?(projectId)
    }

    @objc private func fullSessionButtonClicked() {
        Logger.shared.log(.info, "EarlyEngagement", "🌟 Пользователь выбрал полную сессию (52 мин)")
        hideWindow()

        // Получаем ID проекта если есть
        let projectId = EarlyEngagementSystem.shared.getFocusedProject()?.id
        onFullSession?(projectId)
    }

    @objc private func declineButtonClicked() {
        Logger.shared.log(.info, "EarlyEngagement", "❌ Пользователь отклонил предложение раннего вовлечения")
        hideWindow()
        onDecline?()
    }

    @objc private func snoozeButtonClicked() {
        Logger.shared.log(.info, "EarlyEngagement", "⏰ Пользователь отложил предложение на 30 минут")
        hideWindow()
        onSnooze?()
    }
    
    // MARK: - Window Properties
    
    override var canBecomeKey: Bool {
        return true
    }
    
    override var canBecomeMain: Bool {
        return true
    }
}
