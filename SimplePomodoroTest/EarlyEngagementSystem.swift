import Foundation
import Cocoa

/// Система раннего вовлечения - двумерная эскалация для помощи пользователям начинать работу утром
/// Использует адаптивные планки и принцип "минимального плана"
class EarlyEngagementSystem {
    
    // MARK: - Singleton
    static let shared = EarlyEngagementSystem()
    private init() {
        loadUserBarData()
        loadEngagementHistory()
        Logger.shared.log(.info, "EarlyEngagement", "🌅 EarlyEngagementSystem инициализирован")
    }
    
    // MARK: - Properties
    
    /// Текущее состояние системы
    private var isActive = false
    
    /// Текущая планка пользователя (адаптивная)
    private var currentUserBar: TimeInterval = 52 * 60 // Начальная планка 52 минуты
    
    /// История изменений планки для адаптации
    private var userBarHistory: [UserBarEntry] = []
    
    /// Дни без работы (для ВЕРТИКАЛЬНОЙ эскалации)
    private var daysWithoutWork: Int = 0
    
    /// Время последней работы
    private var lastWorkTime: Date?
    
    /// История событий вовлечения
    private var engagementHistory: [EngagementEvent] = []
    
    /// Флаг сброса эскалации после первого успешного интервала
    private var escalationResetAfterSuccess = false
    
    // MARK: - Dependencies
    
    /// Ссылка на ProjectManager для получения приоритетного проекта
    weak var projectManager: ProjectManager?
    
    /// Ссылка на PomodoroTimer для запуска интервалов
    weak var pomodoroTimer: PomodoroTimer?
    
    /// Ссылка на SleepWakeDetector для получения событий пробуждения
    weak var sleepWakeDetector: SleepWakeDetector?
    
    // MARK: - Delegates & Callbacks
    
    /// Делегат для показа UI сообщений
    weak var delegate: EarlyEngagementSystemDelegate?
    
    /// Колбэк для показа окна раннего вовлечения
    var onShowEngagementWindow: ((EngagementMessage) -> Void)?
    
    /// Колбэк для записи статистики
    var onRecordStatistics: ((EngagementEvent) -> Void)?
    
    // MARK: - Public Methods
    
    /// Запускает систему раннего вовлечения
    func start() {
        guard !isActive else {
            Logger.shared.log(.info, "EarlyEngagement", "🌅 Система уже активна")
            return
        }

        isActive = true
        Logger.shared.log(.info, "EarlyEngagement", "🌅 Система раннего вовлечения запущена")

        // Загружаем данные о последней работе
        loadLastWorkTime()
        calculateDaysWithoutWork()

        // Адаптируем планку по дням без работы
        adaptUserBarByDaysWithoutWork()

        Logger.shared.log(.info, "EarlyEngagement", "📊 Текущая планка: \(Int(currentUserBar/60)) мин, дней без работы: \(daysWithoutWork)")
    }
    
    /// Останавливает систему
    func stop() {
        isActive = false
        Logger.shared.log(.info, "EarlyEngagement", "🌅 Система раннего вовлечения остановлена")
    }
    
    /// Обрабатывает событие пробуждения компьютера
    func handleWakeUpEvent(sleepDuration: TimeInterval, wakeTime: Date) {
        guard isActive else { return }
        
        Logger.shared.log(.info, "EarlyEngagement", "🌅 Обработка пробуждения: сон \(Int(sleepDuration/60)) мин")
        
        // Определяем нужно ли показывать сообщение раннего вовлечения
        if shouldShowEngagementMessage(wakeTime: wakeTime, sleepDuration: sleepDuration) {
            showEngagementMessage()
        }
    }
    
    /// Обрабатывает успешное завершение интервала
    func handleIntervalCompletion(duration: TimeInterval, projectId: UUID?) {
        Logger.shared.log(.info, "EarlyEngagement", "✅ Интервал завершен: \(Int(duration/60)) мин")

        // Обновляем время последней работы только если это приоритетный проект
        if let projectId = projectId, let focusedProject = getFocusedProject(), projectId == focusedProject.id {
            lastWorkTime = Date()
            daysWithoutWork = 0
            Logger.shared.log(.info, "EarlyEngagement", "📅 Работа над приоритетным проектом зафиксирована")
        } else {
            Logger.shared.log(.info, "EarlyEngagement", "📅 Работа не над приоритетным проектом, дни без работы не сбрасываются")
        }

        // Сбрасываем эскалацию после первого успешного интервала
        if !escalationResetAfterSuccess {
            escalationResetAfterSuccess = true
            Logger.shared.log(.info, "EarlyEngagement", "🔄 Эскалация сброшена после первого успешного интервала")
        }
        
        // Адаптируем планку на основе успеха
        adaptUserBar(success: true, intervalDuration: duration)

        // Записываем в статистику
        EngagementStatistics.shared.recordIntervalCompleted(
            duration: duration,
            projectId: projectId,
            wasFromEngagement: true
        )

        // Сохраняем данные
        saveUserBarData()
        saveLastWorkTime()

        // Записываем событие
        recordEngagementEvent(.intervalCompleted, duration: duration, projectId: projectId)
    }
    
    /// Обрабатывает отказ пользователя от интервала
    func handleUserRefusal(proposedDuration: TimeInterval) {
        Logger.shared.log(.info, "EarlyEngagement", "❌ Пользователь отказался от интервала \(Int(proposedDuration/60)) мин")

        // Записываем в статистику
        let (vertical, horizontal) = getCurrentMatrixPosition()
        EngagementStatistics.shared.recordUserRefusal(
            vertical: vertical,
            horizontal: horizontal,
            userBar: currentUserBar
        )

        // Адаптируем планку на основе отказа
        adaptUserBar(success: false, intervalDuration: proposedDuration)
        
        // Записываем событие
        recordEngagementEvent(.userRefused, duration: proposedDuration)
        
        saveUserBarData()
    }

    /// Обрабатывает отклонение пользователем предложения
    func handleUserDecline() {
        Logger.shared.log(.info, "EarlyEngagement", "❌ Пользователь отклонил предложение")

        // Записываем в статистику
        let (vertical, horizontal) = getCurrentMatrixPosition()
        EngagementStatistics.shared.recordUserRefusal(
            vertical: vertical,
            horizontal: horizontal,
            userBar: currentUserBar
        )

        // Адаптируем планку (уменьшаем при отказе)
        adaptUserBar(success: false, intervalDuration: currentUserBar)

        saveUserBarData()
    }

    /// Обрабатывает отложение пользователем предложения
    func handleUserSnooze(proposedDuration: TimeInterval) {
        Logger.shared.log(.info, "EarlyEngagement", "⏰ Пользователь отложил интервал \(Int(proposedDuration/60)) мин")

        // Записываем в статистику
        let (vertical, horizontal) = getCurrentMatrixPosition()
        EngagementStatistics.shared.recordUserSnooze(
            vertical: vertical,
            horizontal: horizontal,
            userBar: currentUserBar
        )

        // Записываем событие отложения
        recordEngagementEvent(.userSnoozed, duration: proposedDuration)
    }

    /// Возвращает текущую планку пользователя
    func getCurrentUserBar() -> TimeInterval {
        return currentUserBar
    }
    
    /// Возвращает приоритетный проект для подстановки в сообщения
    func getFocusedProject() -> Project? {
        return projectManager?.getPriorityProject()
    }

    /// Запускает интервал с адаптивной длительностью
    func startAdaptiveInterval(projectId: UUID? = nil) {
        guard let timer = pomodoroTimer else {
            Logger.shared.log(.info, "EarlyEngagement", "❌ PomodoroTimer не установлен")
            return
        }

        // Устанавливаем адаптивную длительность из текущей планки
        let adaptiveDuration = currentUserBar
        timer.updateWorkDuration(adaptiveDuration)

        Logger.shared.log(.info, "EarlyEngagement", "🚀 Запуск адаптивного интервала: \(Int(adaptiveDuration/60)) мин")

        // Запускаем интервал
        timer.startInterval()

        // Записываем событие принятия пользователем
        recordEngagementEvent(.userAccepted, duration: adaptiveDuration, projectId: projectId)

        // Если есть проект, отмечаем его как использованный
        if let projectId = projectId {
            projectManager?.markProjectAsUsed(projectId)
        }
    }

    /// Обрабатывает согласие пользователя на интервал
    func handleUserAcceptance(projectId: UUID? = nil) {
        Logger.shared.log(.info, "EarlyEngagement", "✅ Пользователь согласился на интервал")

        // Записываем в статистику
        let (vertical, horizontal) = getCurrentMatrixPosition()
        EngagementStatistics.shared.recordUserAcceptance(
            vertical: vertical,
            horizontal: horizontal,
            userBar: currentUserBar,
            projectId: projectId
        )

        startAdaptiveInterval(projectId: projectId)
    }

    /// Обрабатывает согласие пользователя на полную сессию (52 минуты)
    func handleFullSessionAcceptance(projectId: UUID? = nil, context: FullSessionContext) {
        Logger.shared.log(.info, "EarlyEngagement", "🌟 Пользователь выбрал полную сессию (52 мин)")

        // Регистрируем попытку полной сессии
        FullSessionSystem.shared.recordFullSessionAttempt()

        // Записываем в статистику раннего вовлечения
        let (vertical, horizontal) = getCurrentMatrixPosition()
        EngagementStatistics.shared.recordUserAcceptance(
            vertical: vertical,
            horizontal: horizontal,
            userBar: 52 * 60, // Полная сессия
            projectId: projectId
        )

        // Запускаем полную сессию
        startFullSession(projectId: projectId, context: context)
    }

    /// Запускает полную сессию (52 минуты)
    private func startFullSession(projectId: UUID? = nil, context: FullSessionContext) {
        guard let timer = pomodoroTimer else {
            Logger.shared.log(.info, "EarlyEngagement", "❌ PomodoroTimer не установлен")
            return
        }

        let fullSessionDuration: TimeInterval = 52 * 60

        // Устанавливаем колбэк для отслеживания завершения полной сессии
        let originalCallback = timer.onFullIntervalCompleted
        timer.onFullIntervalCompleted = { [weak self] duration in
            // Вызываем оригинальный колбэк
            originalCallback?(duration)

            // Обрабатываем завершение полной сессии
            self?.handleFullSessionCompletion(duration: duration, context: context, projectId: projectId)
        }

        // Запускаем таймер на 52 минуты
        timer.updateWorkDuration(fullSessionDuration)
        timer.startInterval()

        // Устанавливаем проект если указан
        if let projectId = projectId {
            projectManager?.markProjectAsUsed(projectId)
        }

        Logger.shared.log(.info, "EarlyEngagement", "🚀 Запущена полная сессия 52 минуты")
    }

    /// Обрабатывает завершение полной сессии
    private func handleFullSessionCompletion(duration: TimeInterval, context: FullSessionContext, projectId: UUID?) {
        let fullSessionDuration: TimeInterval = 52 * 60

        if duration >= fullSessionDuration * 0.9 { // Считаем успешным если выполнено >= 90%
            Logger.shared.log(.info, "EarlyEngagement", "✅ Полная сессия успешно завершена")

            // Регистрируем успешное завершение
            FullSessionSystem.shared.recordFullSessionCompletion(context: convertToFullSessionContext(context))

            // Обновляем планку пользователя
            let newBar = FullSessionSystem.shared.calculateNewBarAfterFullSession(context: convertToFullSessionContext(context))
            currentUserBar = newBar

            // Сбрасываем эскалацию
            escalationResetAfterSuccess = true

        } else {
            Logger.shared.log(.info, "EarlyEngagement", "❌ Полная сессия не завершена")

            // Регистрируем неудачу
            FullSessionSystem.shared.recordFullSessionFailure()
        }

        saveUserBarData()
    }

    /// Конвертирует контекст из EarlyEngagement в FullSession
    private func convertToFullSessionContext(_ context: FullSessionContext) -> FullSessionSystem.SessionContext {
        let originalBar = currentUserBar

        switch context {
        case .horizontalDescaling:
            return .horizontalDescaling(originalBar: originalBar)
        case .verticalDegradation:
            return .verticalDegradation(originalBar: originalBar)
        case .restCompletion:
            return .restCompletion(currentBar: originalBar)
        case .earlyEngagement:
            return .earlyEngagement(currentBar: originalBar)
        }
    }

    /// Настраивает обратную связь с PomodoroTimer
    func setupPomodoroTimerCallbacks() {
        guard let timer = pomodoroTimer else {
            Logger.shared.log(.info, "EarlyEngagement", "❌ PomodoroTimer не установлен для настройки колбэков")
            return
        }

        // Сохраняем существующий колбэк если есть
        let existingCallback = timer.onFullIntervalCompleted

        // Устанавливаем новый колбэк который вызывает и старый и наш
        timer.onFullIntervalCompleted = { [weak self] duration in
            // Вызываем существующий колбэк
            existingCallback?(duration)

            // ИСПРАВЛЕНО: Получаем реальный projectId из AppDelegate
            let currentProjectId = (NSApplication.shared.delegate as? AppDelegate)?.currentProjectId

            // Обрабатываем завершение интервала в системе раннего вовлечения
            self?.handleIntervalCompletion(duration: duration, projectId: currentProjectId)
        }

        Logger.shared.log(.info, "EarlyEngagement", "🔗 Колбэки PomodoroTimer настроены")
    }
    
    // MARK: - Private Methods
    
    /// Определяет нужно ли показывать сообщение раннего вовлечения
    private func shouldShowEngagementMessage(wakeTime: Date, sleepDuration: TimeInterval) -> Bool {
        // Проверяем время дня (утренние часы предпочтительнее)
        let hour = Calendar.current.component(.hour, from: wakeTime)
        let isGoodTime = hour >= 6 && hour <= 12

        // Проверяем длительность сна (реальный сон vs короткий перерыв)
        let isRealSleep = sleepDuration > 30 * 60 // Больше 30 минут

        // ИСПРАВЛЕНО: Сообщение показывается каждый день, независимо от дней без работы
        // Текст сообщения зависит от количества дней, но показывается всегда

        Logger.shared.log(.info, "EarlyEngagement", "🤔 Анализ: время=\(hour)ч (\(isGoodTime ? "хорошее" : "не очень")), сон=\(Int(sleepDuration/60))мин (\(isRealSleep ? "реальный" : "короткий")), дней без работы=\(daysWithoutWork)")

        return isGoodTime && isRealSleep // Убрали условие needsEngagement
    }
    
    /// Показывает сообщение раннего вовлечения
    private func showEngagementMessage() {
        // Создаем сообщение на основе матрицы
        var message = createEngagementMessage()

        // Добавляем поддержку полной сессии
        message = enhanceMessageWithFullSession(message)

        Logger.shared.log(.info, "EarlyEngagement", "💬 Показываем сообщение: \(message.title), полная сессия: \(message.showFullSessionButton)")

        // Записываем событие показа
        recordEngagementEvent(.messageShown, duration: currentUserBar)

        // Записываем в статистику
        let (vertical, horizontal) = getCurrentMatrixPosition()
        EngagementStatistics.shared.recordMessageShown(
            vertical: vertical,
            horizontal: horizontal,
            userBar: currentUserBar,
            daysWithoutWork: daysWithoutWork
        )

        // Показываем через делегат или колбэк
        delegate?.showEngagementMessage(
            message,
            onAccept: { [weak self] projectId in
                self?.handleUserAcceptance(projectId: projectId)
            },
            onDecline: { [weak self] in
                self?.handleUserDecline()
            },
            onSnooze: { [weak self] in
                self?.handleUserSnooze(proposedDuration: message.proposedDuration)
            },
            onFullSession: { [weak self] projectId in
                self?.handleFullSessionAcceptance(projectId: projectId, context: message.sessionContext)
            }
        )
        onShowEngagementWindow?(message)
    }
    
    /// Создает сообщение на основе матрицы 5x4
    private func createEngagementMessage() -> EngagementMessage {
        // Определяем уровень по дням без работы (ВЕРТИКАЛЬ)
        let verticalLevel = getDaysLevelIndex(daysWithoutWork: daysWithoutWork)

        // Определяем время дня (ГОРИЗОНТАЛЬ)
        let hour = Calendar.current.component(.hour, from: Date())
        let horizontalLevel = getTimeOfDayLevel(hour: hour)

        // Получаем базовое сообщение из матрицы
        let baseMessage = MessageConstructionMatrix.getMessage(vertical: verticalLevel, horizontal: horizontalLevel)
        
        // Подставляем переменные
        let processedMessage = substituteVariables(in: baseMessage)
        
        return processedMessage
    }

    /// Возвращает текущую позицию в матрице (вертикаль, горизонталь)
    private func getCurrentMatrixPosition() -> (vertical: Int, horizontal: Int) {
        let verticalLevel = min(daysWithoutWork, 4) // 0-4
        let hour = Calendar.current.component(.hour, from: Date())
        let horizontalLevel = getTimeOfDayLevel(hour: hour)
        return (verticalLevel, horizontalLevel)
    }

    /// Добавляет поддержку полной сессии к сообщению
    private func enhanceMessageWithFullSession(_ message: EngagementMessage) -> EngagementMessage {
        var enhancedMessage = message

        // Определяем контекст для системы полной сессии
        let context = determineSessionContext(message: message)
        enhancedMessage.sessionContext = context

        // Проверяем нужно ли показывать кнопку полной сессии
        enhancedMessage.showFullSessionButton = FullSessionSystem.shared.shouldShowFullSession(currentBar: message.proposedDuration)

        if enhancedMessage.showFullSessionButton {
            Logger.shared.log(.info, "EarlyEngagement", "✅ Добавлена кнопка полной сессии к сообщению")
        } else {
            Logger.shared.log(.info, "EarlyEngagement", "❌ Кнопка полной сессии не показана (планка=\(Int(message.proposedDuration/60))мин)")
        }

        return enhancedMessage
    }

    /// Определяет контекст сессии для системы полной сессии
    private func determineSessionContext(message: EngagementMessage) -> FullSessionContext {
        // Получаем исходную планку пользователя
        let originalBar = currentUserBar

        // Если предлагаемая длительность меньше исходной планки, это дескалация
        if message.proposedDuration < originalBar {
            // Проверяем тип дескалации
            if message.timeOfDay > 0 {
                // Горизонтальная дескалация (время дня)
                return .horizontalDescaling
            } else if message.level > 0 {
                // Вертикальная деградация (дни без работы)
                return .verticalDegradation
            }
        }

        // По умолчанию - раннее вовлечение
        return .earlyEngagement
    }

    /// Определяет уровень времени дня (0-3)
    private func getTimeOfDayLevel(hour: Int) -> Int {
        switch hour {
        case 6..<12: return 0  // Утро
        case 12..<18: return 1 // День
        case 18..<22: return 2 // Вечер
        default: return 3      // Ночь
        }
    }
    
    /// Подставляет переменные в сообщение
    func substituteVariables(in message: EngagementMessage) -> EngagementMessage {
        var processedMessage = message
        
        // Подставляем [focused_project]
        if let focusedProject = getFocusedProject() {
            processedMessage.title = processedMessage.title.replacingOccurrences(of: "[focused_project]", with: focusedProject.name)
            processedMessage.subtitle = processedMessage.subtitle.replacingOccurrences(of: "[focused_project]", with: focusedProject.name)
        } else {
            // Fallback для случая отсутствия приоритетного проекта
            processedMessage.title = processedMessage.title.replacingOccurrences(of: "[focused_project]", with: "важным проектом")
            processedMessage.subtitle = processedMessage.subtitle.replacingOccurrences(of: "[focused_project]", with: "важным проектом")
        }
        
        // Подставляем [current_bar]
        let currentBarMinutes = Int(currentUserBar / 60)
        processedMessage.title = processedMessage.title.replacingOccurrences(of: "[current_bar]", with: "\(currentBarMinutes)")
        processedMessage.subtitle = processedMessage.subtitle.replacingOccurrences(of: "[current_bar]", with: "\(currentBarMinutes)")
        
        // Обновляем предлагаемую длительность
        processedMessage.proposedDuration = currentUserBar
        
        return processedMessage
    }
    
    /// Адаптирует планку по дням без работы согласно документации
    private func adaptUserBarByDaysWithoutWork() {
        let baseBar: TimeInterval = 52 * 60 // Базовая планка 52 минуты
        let oldBar = currentUserBar

        switch daysWithoutWork {
        case 0:
            // Уровень 0 (работал вчера): планка растет по градационной системе
            currentUserBar = GradualGrowthSystem.applyLevel0Growth(currentBar: currentUserBar)
        case 1:
            // Уровень 1 (1 день пропуск): планка × 0.77 (-23%) = ~40 мин
            currentUserBar = baseBar * 0.77
        case 2...3:
            // Уровень 2-3 (2-3 дня): планка × 0.48 (-52%) = ~25 мин
            currentUserBar = baseBar * 0.48
        case 4...6:
            // Уровень 4-6 (4-6 дней): планка × 0.29 (-71%) = ~15 мин
            currentUserBar = baseBar * 0.29
        default:
            // Уровень 7+ (неделя+): планка = 3 минуты (план-минимум согласно документации)
            currentUserBar = 3 * 60
        }

        Logger.shared.log(.info, "EarlyEngagement", "📊 Адаптация планки по дням: \(Int(oldBar/60))мин → \(Int(currentUserBar/60))мин (дней без работы: \(daysWithoutWork))")
    }

    /// Адаптирует планку пользователя на основе успеха/неудачи
    private func adaptUserBar(success: Bool, intervalDuration: TimeInterval) {
        let oldBar = currentUserBar

        if success {
            // При успехе используем градационную систему роста
            currentUserBar = GradualGrowthSystem.calculateGrowth(currentBar: currentUserBar)
            currentUserBar = min(currentUserBar, 52 * 60) // Максимум 52 минуты по документации
        } else {
            // При отказе уменьшаем планку на 15%
            currentUserBar = max(currentUserBar * 0.85, 2 * 60) // Минимум 2 минуты
        }
        
        // Записываем изменение в историю
        let entry = UserBarEntry(
            timestamp: Date(),
            oldValue: oldBar,
            newValue: currentUserBar,
            reason: success ? .success : .failure,
            intervalDuration: intervalDuration
        )
        userBarHistory.append(entry)
        
        // Ограничиваем размер истории
        if userBarHistory.count > 100 {
            userBarHistory.removeFirst()
        }
        
        Logger.shared.log(.info, "EarlyEngagement", "📊 Планка изменена: \(Int(oldBar/60))мин → \(Int(currentUserBar/60))мин (\(success ? "успех" : "неудача"))")
    }
    
    /// Вычисляет количество дней без работы
    private func calculateDaysWithoutWork() -> Int {
        guard let lastWork = lastWorkTime else {
            let result = 7 // Если нет данных, считаем максимальную эскалацию (7+ дней)
            daysWithoutWork = result
            Logger.shared.log(.info, "EarlyEngagement", "📅 Дней без работы: \(result) (никогда не работал над приоритетным проектом)")
            return result
        }

        // ИСПРАВЛЕНО: Правильный подсчет дней
        // Если последняя работа была вчера или раньше, считаем дни
        let calendar = Calendar.current
        let now = Date()

        // Получаем начало сегодняшнего дня
        let startOfToday = calendar.startOfDay(for: now)

        // Получаем день последней работы
        let startOfLastWorkDay = calendar.startOfDay(for: lastWork)

        // Считаем разность в днях
        let daysSince = calendar.dateComponents([.day], from: startOfLastWorkDay, to: startOfToday).day ?? 0
        let result = max(0, daysSince)
        daysWithoutWork = result

        Logger.shared.log(.info, "EarlyEngagement", "📅 Дней без работы: \(result) (последняя работа: \(lastWork), сегодня: \(startOfToday))")
        return result
    }

    /// Получает текущее время дня для горизонтальной эскалации
    private func getCurrentTimeOfDay() -> Int {
        let hour = Calendar.current.component(.hour, from: Date())

        // Утро (6-12): 0
        // День (12-18): 1
        // Вечер (18-22): 2
        // Ночь (22-6): 3
        switch hour {
        case 6..<12: return 0
        case 12..<18: return 1
        case 18..<22: return 2
        default: return 3
        }
    }

    /// Записывает событие вовлечения
    private func recordEngagementEvent(_ type: EngagementEventType, duration: TimeInterval, projectId: UUID? = nil) {
        let (vertical, horizontal) = getCurrentMatrixPosition()
        let event = EngagementEvent(
            type: type,
            timestamp: Date(),
            vertical: vertical,
            horizontal: horizontal,
            userBar: currentUserBar,
            daysWithoutWork: daysWithoutWork,
            projectId: projectId,
            intervalDuration: duration
        )
        
        engagementHistory.append(event)
        
        // Ограничиваем размер истории
        if engagementHistory.count > 1000 {
            engagementHistory.removeFirst()
        }
        
        // Уведомляем о событии
        onRecordStatistics?(event)
        
        saveEngagementHistory()
    }

    // MARK: - Data Persistence

    /// Загружает данные о планке пользователя
    private func loadUserBarData() {
        if let data = UserDefaults.standard.data(forKey: "earlyEngagement_userBar"),
           let decoded = try? JSONDecoder().decode(TimeInterval.self, from: data) {
            currentUserBar = decoded
            Logger.shared.log(.info, "EarlyEngagement", "📊 Загружена планка: \(Int(currentUserBar/60)) мин")
        }

        if let data = UserDefaults.standard.data(forKey: "earlyEngagement_userBarHistory"),
           let decoded = try? JSONDecoder().decode([UserBarEntry].self, from: data) {
            userBarHistory = decoded
            Logger.shared.log(.info, "EarlyEngagement", "📊 Загружена история планки: \(userBarHistory.count) записей")
        }
    }

    /// Сохраняет данные о планке пользователя
    private func saveUserBarData() {
        if let encoded = try? JSONEncoder().encode(currentUserBar) {
            UserDefaults.standard.set(encoded, forKey: "earlyEngagement_userBar")
        }

        if let encoded = try? JSONEncoder().encode(userBarHistory) {
            UserDefaults.standard.set(encoded, forKey: "earlyEngagement_userBarHistory")
        }
    }

    /// Загружает историю событий вовлечения
    private func loadEngagementHistory() {
        if let data = UserDefaults.standard.data(forKey: "earlyEngagement_history"),
           let decoded = try? JSONDecoder().decode([EngagementEvent].self, from: data) {
            engagementHistory = decoded
            Logger.shared.log(.info, "EarlyEngagement", "📊 Загружена история событий: \(engagementHistory.count) записей")
        }
    }

    /// Сохраняет историю событий вовлечения
    private func saveEngagementHistory() {
        if let encoded = try? JSONEncoder().encode(engagementHistory) {
            UserDefaults.standard.set(encoded, forKey: "earlyEngagement_history")
        }
    }

    /// Загружает время последней работы
    private func loadLastWorkTime() {
        if let timestamp = UserDefaults.standard.object(forKey: "earlyEngagement_lastWork") as? Date {
            lastWorkTime = timestamp
            Logger.shared.log(.info, "EarlyEngagement", "📅 Загружено время последней работы: \(timestamp)")
        }
    }

    /// Сохраняет время последней работы
    private func saveLastWorkTime() {
        if let lastWork = lastWorkTime {
            UserDefaults.standard.set(lastWork, forKey: "earlyEngagement_lastWork")
        }
    }

    // MARK: - Debug Methods

    /// Преобразует количество дней без работы в индекс матрицы
    private func getDaysLevelIndex(daysWithoutWork: Int) -> Int {
        switch daysWithoutWork {
        case 0:
            return 0  // Уровень 0 (работал вчера)
        case 1:
            return 1  // Уровень 1 (1 день пропуск)
        case 2...3:
            return 2  // Уровень 2-3 (2-3 дня)
        case 4...6:
            return 3  // Уровень 4-6 (4-6 дней)
        default:
            return 4  // Уровень 7+ (неделя+)
        }
    }

    /// Получает текущее сообщение для отладки
    func getCurrentMessage() -> EngagementMessage {
        let daysWithoutWork = calculateDaysWithoutWork()
        let timeOfDay = getCurrentTimeOfDay()
        let daysLevelIndex = getDaysLevelIndex(daysWithoutWork: daysWithoutWork)

        Logger.shared.log(.info, "EarlyEngagement", "🔧 Генерация сообщения для отладки: дни=\(daysWithoutWork), уровень=\(daysLevelIndex), время=\(timeOfDay)")

        let message = MessageConstructionMatrix.getMessage(vertical: daysLevelIndex, horizontal: timeOfDay)
        return substituteVariables(in: message)
    }

    /// Принудительно показывает сообщение раннего вовлечения для тестирования
    func forceShowMessage() {
        Logger.shared.log(.info, "EarlyEngagement", "🔧 Принудительный показ сообщения для отладки")

        // Получаем текущее сообщение
        let message = getCurrentMessage()

        // Записываем статистику показа
        EngagementStatistics.shared.recordMessageShown(
            vertical: daysWithoutWork,
            horizontal: getCurrentTimeOfDay(),
            userBar: currentUserBar,
            daysWithoutWork: daysWithoutWork
        )

        // Показываем через делегат
        delegate?.showEngagementMessage(
            message,
            onAccept: { [weak self] projectId in
                Logger.shared.log(.info, "EarlyEngagement", "🔧 Отладка: Пользователь принял предложение")
                self?.handleUserAcceptance(projectId: projectId)
            },
            onDecline: { [weak self] in
                Logger.shared.log(.info, "EarlyEngagement", "🔧 Отладка: Пользователь отклонил предложение")
                self?.handleUserDecline()
            },
            onSnooze: { [weak self] in
                Logger.shared.log(.info, "EarlyEngagement", "🔧 Отладка: Пользователь отложил предложение")
                self?.handleUserSnooze(proposedDuration: 25 * 60) // 25 минут для отладки
            },
            onFullSession: { [weak self] projectId in
                Logger.shared.log(.info, "EarlyEngagement", "🔧 Отладка: Пользователь выбрал полную сессию")
                self?.handleFullSessionAcceptance(projectId: projectId, context: .earlyEngagement)
            }
        )
    }

    /// Получает текущие значения для отладки
    var debugCurrentUserBar: TimeInterval {
        return currentUserBar
    }

    var debugDaysWithoutWork: Int {
        return daysWithoutWork
    }

    var debugLastWorkTime: Date? {
        return lastWorkTime
    }

    var debugIsActive: Bool {
        return isActive
    }

    var debugCurrentTimeOfDay: Int {
        return getCurrentTimeOfDay()
    }

    // MARK: - Debug Methods

    func debugGetCurrentUserBar() -> TimeInterval {
        return currentUserBar
    }

    func debugGetDaysWithoutWork() -> Int {
        return daysWithoutWork
    }

    func debugGetLastWorkTime() -> Date? {
        return lastWorkTime
    }

    func debugGetIsActive() -> Bool {
        return isActive
    }

    /// Рассчитывает планку для отладочного сценария
    func debugCalculateBarForScenario(initialBar: Int, daysWithoutWork: Int, messageIndex: Int) -> Int {
        let initialBarTime = TimeInterval(initialBar * 60)

        // ВЕРТИКАЛЬНАЯ адаптация (по дням без работы) - используем ту же логику что в adaptUserBarByDaysWithoutWork
        let verticalBarTime: TimeInterval
        switch daysWithoutWork {
        case 0:
            // Уровень 0 (работал вчера): планка растет по градационной системе
            verticalBarTime = GradualGrowthSystem.applyLevel0Growth(currentBar: initialBarTime)
        case 1:
            // Уровень 1 (1 день пропуск): планка × 0.77 (-23%) = ~40 мин
            verticalBarTime = initialBarTime * 0.77
        case 2...3:
            // Уровень 2-3 (2-3 дня): планка × 0.48 (-52%) = ~25 мин
            verticalBarTime = initialBarTime * 0.48
        case 4...6:
            // Уровень 4-6 (4-6 дней): планка × 0.29 (-71%) = ~15 мин
            verticalBarTime = initialBarTime * 0.29
        default:
            // Уровень 7+ (неделя+): планка = 3 минуты (план-минимум согласно документации)
            verticalBarTime = 3 * 60
        }

        // ГОРИЗОНТАЛЬНАЯ дескалация (по времени дня) - согласно документации
        let finalBarTime: TimeInterval
        switch messageIndex {
        case 0:
            // 1-е предложение: 100% планки (но не меньше план-минимума)
            finalBarTime = max(verticalBarTime, 3 * 60)
        case 1:
            // 2-е предложение: 50% планки (но не меньше план-минимума)
            finalBarTime = max(verticalBarTime * 0.5, 3 * 60)
        case 2:
            // 3-е предложение: фиксированный минимум (15 мин)
            finalBarTime = 15 * 60
        case 3:
            // 4-е предложение: план-минимум (3 мин)
            finalBarTime = 3 * 60
        default:
            finalBarTime = max(verticalBarTime, 3 * 60)
        }

        // Ограничиваем максимум 52 минуты
        let limitedBarTime = min(finalBarTime, 52 * 60)

        return Int(limitedBarTime / 60)
    }

    /// Генерирует текст формулы для отладки
    func debugGetFormulaText(daysLevel: Int, messageIndex: Int, initialBar: Int, calculatedBar: Int) -> String {
        // Сначала показываем вертикальную адаптацию
        let verticalText: String
        switch daysLevel {
        case 0:
            let growthDesc = GradualGrowthSystem.getGrowthDescription(currentBarMinutes: initialBar)
            verticalText = "\(initialBar) \(growthDesc)"
        case 1:
            verticalText = "\(initialBar) × 0.77"
        case 2...3:
            verticalText = "\(initialBar) × 0.58"
        case 4...6:
            verticalText = "\(initialBar) × 0.29"
        default:
            verticalText = "план-минимум"
        }

        // Затем показываем горизонтальную дескалацию
        let horizontalText: String
        switch messageIndex {
        case 0:
            horizontalText = "× 1.0 (100%)"
        case 1:
            horizontalText = "× 0.5 (50%)"
        case 2:
            horizontalText = "→ 15 мин (фикс.)"
        case 3:
            horizontalText = "→ 3 мин (план-мин.)"
        default:
            horizontalText = ""
        }

        if daysLevel >= 7 && messageIndex >= 2 {
            return "план-минимум = \(calculatedBar) мин"
        } else if messageIndex >= 2 {
            return "\(verticalText) \(horizontalText) = \(calculatedBar) мин"
        } else {
            return "\(verticalText) \(horizontalText) = \(calculatedBar) мин"
        }
    }

    func debugGetCurrentMessage() -> EngagementMessage? {
        let timeOfDay = getCurrentTimeOfDay()
        let vertical = getDaysLevelIndex(daysWithoutWork: daysWithoutWork)
        let horizontal = timeOfDay
        return MessageConstructionMatrix.getMessage(vertical: vertical, horizontal: horizontal)
    }

    func debugCreateDemoMessage() -> EngagementMessage {
        let vertical = getDaysLevelIndex(daysWithoutWork: max(1, daysWithoutWork))
        let horizontal = 0 // morning
        return MessageConstructionMatrix.getMessage(vertical: vertical, horizontal: horizontal)
    }

    func debugSimulateWakeEvent() {
        Logger.shared.log(.info, "EarlyEngagement", "🔧 Debug: Симуляция события пробуждения")
        // Симулируем пробуждение после 8 часов сна
        let sleepDuration: TimeInterval = 8 * 60 * 60 // 8 часов
        let wakeTime = Date()
        handleWakeUpEvent(sleepDuration: sleepDuration, wakeTime: wakeTime)
    }

    func debugRecalculateDaysWithoutWork() {
        Logger.shared.log(.info, "EarlyEngagement", "🔧 Debug: Принудительный пересчет дней без работы")
        calculateDaysWithoutWork()
        adaptUserBarByDaysWithoutWork()
        saveUserBarData()
    }

    func debugResetLastWorkTime() {
        Logger.shared.log(.info, "EarlyEngagement", "🔧 Debug: Сброс времени последней работы")
        lastWorkTime = nil
        UserDefaults.standard.removeObject(forKey: "earlyEngagement_lastWork")
        calculateDaysWithoutWork()
        adaptUserBarByDaysWithoutWork()
        saveUserBarData()
        Logger.shared.log(.info, "EarlyEngagement", "✅ Время последней работы сброшено")
    }
}

// MARK: - Supporting Data Structures

/// Сообщение раннего вовлечения
struct EngagementMessage: Codable {
    var title: String
    var subtitle: String
    var proposedDuration: TimeInterval
    var buttonText: String
    var level: Int // Уровень эскалации (0-4)
    var timeOfDay: Int // Время дня (0-3)

    // Поддержка системы полной сессии
    var showFullSessionButton: Bool = false
    var fullSessionButtonText: String = "Полная сессия (52 мин)"
    var sessionContext: FullSessionContext = .earlyEngagement
}

/// Контекст для системы полной сессии
enum FullSessionContext: String, Codable {
    case earlyEngagement = "early_engagement"
    case horizontalDescaling = "horizontal_descaling"
    case verticalDegradation = "vertical_degradation"
    case restCompletion = "rest_completion"
}

/// Запись изменения планки пользователя
struct UserBarEntry: Codable {
    let timestamp: Date
    let oldValue: TimeInterval
    let newValue: TimeInterval
    let reason: UserBarChangeReason
    let intervalDuration: TimeInterval
}

/// Причина изменения планки
enum UserBarChangeReason: String, Codable {
    case success = "success"
    case failure = "failure"
    case adaptation = "adaptation"
    case reset = "reset"
}

// MARK: - Protocols

/// Протокол делегата системы раннего вовлечения
protocol EarlyEngagementSystemDelegate: AnyObject {
    /// Показать сообщение раннего вовлечения с колбэками
    func showEngagementMessage(
        _ message: EngagementMessage,
        onAccept: @escaping (UUID?) -> Void,
        onDecline: @escaping () -> Void,
        onSnooze: @escaping () -> Void,
        onFullSession: @escaping (UUID?) -> Void
    )

    /// Запустить интервал с указанной длительностью
    func startInterval(duration: TimeInterval, projectId: UUID?)

    /// Записать статистику события
    func recordEngagementStatistics(_ event: EngagementEvent)
}
