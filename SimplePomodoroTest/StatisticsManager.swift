import Foundation

class StatisticsManager {
    private let userDefaults = UserDefaults.standard
    private let completedIntervalsKey = "completedIntervals"
    private let completedBreaksKey = "completedBreaks"
    private let computerTimeKey = "computerTime"
    private let activityRecordsKey = "activityRecords"  // НОВОЕ: для 4-бандовой системы
    private let restStatisticsKey = "restStatistics"   // НОВОЕ: для расширенной статистики отдыха

    init() {
        // Инициализация может включать миграцию данных если нужно
    }

    // MARK: - Запись статистики
    
    func recordCompletedInterval(duration: TimeInterval, projectId: UUID? = nil, intervalType: String = "formal") {
        let now = Date()
        let interval = CompletedInterval(date: now, duration: duration, projectId: projectId, intervalType: intervalType)

        var intervals = getAllStoredIntervals()
        intervals.append(interval)

        saveIntervals(intervals)

        let projectInfo = projectId != nil ? " для проекта \(projectId!)" : ""
        let typeInfo = intervalType != "formal" ? " (\(intervalType))" : ""
        print("📊 StatisticsManager: Записан полноценный интервал. Продолжительность: \(Int(duration/60)) мин\(projectInfo)\(typeInfo)")
    }

    func recordCompletedBreak(duration: TimeInterval, wasComputerActive: Bool, computerActiveTime: TimeInterval, qualityPercentage: Int = 100) {
        let now = Date()
        let breakRecord = CompletedBreak(
            date: now,
            duration: duration,
            wasComputerActive: wasComputerActive,
            computerActiveTime: computerActiveTime,
            qualityPercentage: qualityPercentage
        )

        var breaks = getAllStoredBreaks()
        breaks.append(breakRecord)

        saveBreaks(breaks)

        let activityInfo = wasComputerActive ? "с активностью (\(Int(computerActiveTime)) сек)" : "без активности"
        print("🌿 StatisticsManager: Записан отдых. Продолжительность: \(Int(duration/60)) мин, \(activityInfo), качество: \(qualityPercentage)%")
    }

    // MARK: - НОВОЕ: Запись активности (4-бандовая система)

    /// Записывает состояние активности с данными 4-бандовой системы
    func recordActivityState(state: ActivityState, duration: TimeInterval, bandData: [Bool] = []) {
        let now = Date()
        let record = ActivityRecord(
            date: now,
            state: state,
            duration: duration,
            bandData: bandData
        )

        var records = getAllStoredActivityRecords()
        records.append(record)

        saveActivityRecords(records)

        let bandInfo = bandData.isEmpty ? "" : " (банды: \(bandData))"
        print("🎯 StatisticsManager: Записано состояние активности \(state), продолжительность: \(Int(duration)) сек\(bandInfo)")
    }

    /// Записывает переход между состояниями активности
    func recordActivityTransition(fromState: ActivityState, toState: ActivityState, duration: TimeInterval) {
        // Записываем завершение предыдущего состояния
        recordActivityState(state: fromState, duration: duration)

        print("🎯 StatisticsManager: Переход активности: \(fromState) → \(toState) (длительность: \(Int(duration)) сек)")
    }

    // MARK: - НОВОЕ: Запись расширенной статистики отдыха (4.1)

    /// Записывает расширенную статистику отдыха с типом, качеством и контекстом
    func recordRestStatistics(duration: TimeInterval, type: RestType, quality: RestQuality, context: RestContext, wasComputerActive: Bool = false, computerActiveTime: TimeInterval = 0, bandData: [Bool] = []) {
        let now = Date()
        let restStats = RestStatistics(
            date: now,
            duration: duration,
            type: type,
            quality: quality,
            context: context,
            wasComputerActive: wasComputerActive,
            computerActiveTime: computerActiveTime,
            bandData: bandData
        )

        var allRestStats = getAllStoredRestStatistics()
        allRestStats.append(restStats)

        saveRestStatistics(allRestStats)

        let activityInfo = wasComputerActive ? " (активность: \(Int(computerActiveTime)) сек)" : ""
        let bandInfo = bandData.isEmpty ? "" : " (банды: \(bandData))"
        print("🌿 StatisticsManager: Записана расширенная статистика отдыха: \(type) \(quality) \(Int(duration/60)) мин\(activityInfo)\(bandInfo)")
    }

    /// Автоматическая запись отдыха на основе времени неактивности (умная логика)
    func recordSmartRestStatistics(inactivityDuration: TimeInterval, context: RestContext, wasComputerActive: Bool = false, computerActiveTime: TimeInterval = 0, userChoice: String? = nil) {
        let (type, quality) = determineRestTypeAndQuality(duration: inactivityDuration, userChoice: userChoice)

        recordRestStatistics(
            duration: inactivityDuration,
            type: type,
            quality: quality,
            context: context,
            wasComputerActive: wasComputerActive,
            computerActiveTime: computerActiveTime
        )

        print("🌿 StatisticsManager: Умная запись отдыха: \(Int(inactivityDuration/60)) мин → \(type) \(quality)")
    }

    /// Определяет тип и качество отдыха на основе длительности и выбора пользователя
    private func determineRestTypeAndQuality(duration: TimeInterval, userChoice: String?) -> (RestType, RestQuality) {
        let minutes = duration / 60.0

        switch minutes {
        case 0..<2:
            return (.micro, .good)  // Микропауза - нормально

        case 2..<10:
            if userChoice == "continue_break" {
                return (.partial, .good)  // Продолжил отдых - хорошо
            } else {
                return (.partial, .poor)  // Прервал отдых - плохо
            }

        case 10..<17:
            if userChoice == "continue_break" {
                return (.partial, .good)  // Продолжил отдых - хорошо
            } else if userChoice == "start_interval" {
                return (.partial, .fair)  // Начал работу - нормально
            } else {
                return (.partial, .good)  // По умолчанию хорошо
            }

        default: // 17+ минут
            return (.automatic, .excellent)  // Полный автоматический отдых - отлично
        }
    }

    // MARK: - Получение статистики
    
    func getStatsForToday() -> Int {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        return getCompletedIntervalsForDateRange(from: today, to: tomorrow)
    }
    
    func getStatsForYesterday() -> Int {
        let today = Calendar.current.startOfDay(for: Date())
        let yesterday = Calendar.current.date(byAdding: .day, value: -1, to: today)!
        return getCompletedIntervalsForDateRange(from: yesterday, to: today)
    }

    // MARK: - Статистика качества отдыха

    /// Получает среднее качество отдыха за указанный период (только рабочие дни)
    func getAverageBreakQualityForPeriod(days: Int) -> Int {
        let endDate = Date()
        let startDate = Calendar.current.date(byAdding: .day, value: -days, to: endDate)!

        let breaks = getAllStoredBreaks()
        let periodBreaks = breaks.filter { $0.date >= startDate && $0.date <= endDate }

        // Группируем отдыхи по дням
        let calendar = Calendar.current
        let groupedByDay = Dictionary(grouping: periodBreaks) { breakRecord in
            calendar.startOfDay(for: breakRecord.date)
        }

        // Считаем среднее качество только для дней когда были отдыхи (рабочие дни)
        let workingDaysQualities = groupedByDay.compactMap { (day, dayBreaks) -> Int? in
            guard !dayBreaks.isEmpty else { return nil }

            let totalQuality = dayBreaks.reduce(0) { $0 + $1.qualityPercentage }
            return totalQuality / dayBreaks.count
        }

        guard !workingDaysQualities.isEmpty else { return -1 }  // Специальное значение "нет данных"

        let averageQuality = workingDaysQualities.reduce(0, +) / workingDaysQualities.count

        print("📊 StatisticsManager: Среднее качество отдыха за \(days) дней: \(averageQuality)% (рабочих дней: \(workingDaysQualities.count))")

        return averageQuality
    }
    
    func getStatsForCurrentWeek() -> Int {
        let calendar = Calendar.current
        let now = Date()
        
        // Получаем начало недели (понедельник)
        let weekday = calendar.component(.weekday, from: now)
        let daysFromMonday = (weekday == 1) ? 6 : weekday - 2 // Воскресенье = 1, Понедельник = 2
        let startOfWeek = calendar.date(byAdding: .day, value: -daysFromMonday, to: calendar.startOfDay(for: now))!
        let endOfWeek = calendar.date(byAdding: .day, value: 7, to: startOfWeek)!
        
        return getCompletedIntervalsForDateRange(from: startOfWeek, to: endOfWeek)
    }
    
    func getStatsForCurrentMonth() -> Int {
        let calendar = Calendar.current
        let now = Date()
        
        let startOfMonth = calendar.dateInterval(of: .month, for: now)!.start
        let endOfMonth = calendar.dateInterval(of: .month, for: now)!.end
        
        return getCompletedIntervalsForDateRange(from: startOfMonth, to: endOfMonth)
    }
    
    func getCompletedIntervalsForDateRange(from startDate: Date, to endDate: Date) -> Int {
        let intervals = getAllStoredIntervals()
        return intervals.filter { interval in
            interval.date >= startDate && interval.date < endDate
        }.count
    }
    
    func getRecentIntervals(limit: Int = 10) -> [(Date, TimeInterval)] {
        let intervals = getAllStoredIntervals()
        return Array(intervals.suffix(limit).reversed()).map { ($0.date, $0.duration) }
    }

    func getAllIntervals() -> [(Date, TimeInterval)] {
        let intervals = getAllStoredIntervals()
        return intervals.map { ($0.date, $0.duration) }
    }
    
    func getTotalCompletedIntervals() -> Int {
        return getAllStoredIntervals().count
    }

    /// Получает количество интервалов по типу
    func getCompletedIntervalsByType(intervalType: String) -> Int {
        let intervals = getAllStoredIntervals()
        return intervals.filter { $0.intervalType == intervalType }.count
    }

    /// Получает статистику по типам интервалов
    func getIntervalTypeStatistics() -> (formal: Int, informal: Int) {
        let intervals = getAllStoredIntervals()
        let formal = intervals.filter { $0.intervalType == "formal" }.count
        let informal = intervals.filter { $0.intervalType == "informal" }.count
        return (formal: formal, informal: informal)
    }

    // MARK: - Break Statistics

    func getBreaksForToday() -> Int {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        return getCompletedBreaksForDateRange(from: today, to: tomorrow)
    }

    func getBreaksForCurrentWeek() -> Int {
        let calendar = Calendar.current
        let now = Date()

        let startOfWeek = calendar.dateInterval(of: .weekOfYear, for: now)!.start
        let endOfWeek = calendar.dateInterval(of: .weekOfYear, for: now)!.end

        return getCompletedBreaksForDateRange(from: startOfWeek, to: endOfWeek)
    }

    func getCompletedBreaksForDateRange(from startDate: Date, to endDate: Date) -> Int {
        let breaks = getAllStoredBreaks()
        return breaks.filter { breakRecord in
            breakRecord.date >= startDate && breakRecord.date < endDate
        }.count
    }

    func getBreakQualityForDateRange(from startDate: Date, to endDate: Date) -> (total: Int, withActivity: Int, qualityScore: Double) {
        let breaks = getAllStoredBreaks().filter { breakRecord in
            breakRecord.date >= startDate && breakRecord.date < endDate
        }

        let total = breaks.count
        let withActivity = breaks.filter { $0.wasComputerActive }.count

        // Отладочная информация
        print("🔍 StatisticsManager: getBreakQualityForDateRange")
        print("🔍 Период: \(startDate) - \(endDate)")
        print("🔍 Всего отдыхов в базе: \(getAllStoredBreaks().count)")
        print("🔍 Отдыхов в периоде: \(total)")
        if total > 0 {
            print("🔍 Детали отдыхов:")
            for (index, breakRecord) in breaks.enumerated() {
                print("🔍   \(index + 1). Дата: \(breakRecord.date), Качество: \(breakRecord.qualityPercentage)%")
            }
        }

        // Используем новое поле qualityPercentage если доступно, иначе старую логику
        let qualityScore: Double
        if total > 0 {
            let totalQuality = breaks.reduce(0) { $0 + $1.qualityPercentage }
            qualityScore = Double(totalQuality) / Double(total) / 100.0  // Конвертируем проценты в 0-1
            print("🔍 Рассчитанное качество: \(qualityScore) (из \(totalQuality) / \(total))")
        } else {
            qualityScore = -1.0  // Специальное значение "нет данных"
            print("🔍 Нет отдыхов - возвращаем -1.0")
        }

        return (total: total, withActivity: withActivity, qualityScore: qualityScore)
    }

    func getRecentBreaks(limit: Int = 10) -> [CompletedBreak] {
        let breaks = getAllStoredBreaks()
        return Array(breaks.suffix(limit).reversed())
    }

    // MARK: - Project-based Statistics

    /// Возвращает статистику по проекту за указанный период
    func getStatsForProject(_ projectId: UUID, from startDate: Date, to endDate: Date) -> Int {
        let intervals = getAllStoredIntervals()
        return intervals.filter { interval in
            interval.projectId == projectId && interval.date >= startDate && interval.date < endDate
        }.count
    }

    /// Возвращает статистику по проекту за сегодня
    func getStatsForProjectToday(_ projectId: UUID) -> Int {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        return getStatsForProject(projectId, from: today, to: tomorrow)
    }

    /// Возвращает статистику по проекту за текущую неделю
    func getStatsForProjectCurrentWeek(_ projectId: UUID) -> Int {
        let calendar = Calendar.current
        let now = Date()

        let weekday = calendar.component(.weekday, from: now)
        let daysFromMonday = (weekday == 1) ? 6 : weekday - 2
        let startOfWeek = calendar.date(byAdding: .day, value: -daysFromMonday, to: calendar.startOfDay(for: now))!
        let endOfWeek = calendar.date(byAdding: .day, value: 7, to: startOfWeek)!

        return getStatsForProject(projectId, from: startOfWeek, to: endOfWeek)
    }

    /// Возвращает статистику по проекту за текущий месяц
    func getStatsForProjectCurrentMonth(_ projectId: UUID) -> Int {
        let calendar = Calendar.current
        let now = Date()

        let startOfMonth = calendar.dateInterval(of: .month, for: now)!.start
        let endOfMonth = calendar.dateInterval(of: .month, for: now)!.end

        return getStatsForProject(projectId, from: startOfMonth, to: endOfMonth)
    }

    /// Возвращает все интервалы для конкретного проекта
    func getIntervalsForProject(_ projectId: UUID) -> [(Date, TimeInterval)] {
        let intervals = getAllStoredIntervals()
        return intervals.filter { $0.projectId == projectId }.map { ($0.date, $0.duration) }
    }

    /// Возвращает интервалы без проекта (для миграции старых данных)
    func getIntervalsWithoutProject() -> [(Date, TimeInterval)] {
        let intervals = getAllStoredIntervals()
        return intervals.filter { $0.projectId == nil }.map { ($0.date, $0.duration) }
    }

    /// Возвращает статистику по всем проектам
    func getProjectStatistics() -> [UUID: Int] {
        let intervals = getAllStoredIntervals()
        var projectStats: [UUID: Int] = [:]

        for interval in intervals {
            if let projectId = interval.projectId {
                projectStats[projectId, default: 0] += 1
            }
        }

        return projectStats
    }

    // MARK: - НОВОЕ: Получение статистики активности (4-бандовая система)

    /// Возвращает время в каждом состоянии активности за указанный период
    func getActivityStatsForDateRange(from startDate: Date, to endDate: Date) -> [ActivityState: TimeInterval] {
        let records = getAllStoredActivityRecords().filter { record in
            record.date >= startDate && record.date < endDate
        }

        var stats: [ActivityState: TimeInterval] = [:]

        for record in records {
            stats[record.state, default: 0] += record.duration
        }

        print("🎯 StatisticsManager: Статистика активности за период \(startDate) - \(endDate):")
        for (state, duration) in stats {
            print("🎯   \(state): \(Int(duration/60)) мин")
        }

        return stats
    }

    /// Возвращает детальные записи активности за указанный период
    func getActivityRecordsForDateRange(from startDate: Date, to endDate: Date) -> [ActivityRecord] {
        let records = getAllStoredActivityRecords().filter { record in
            record.date >= startDate && record.date < endDate
        }

        return records.sorted { $0.date < $1.date }
    }

    /// Возвращает качество активности за указанный период (% времени в состоянии .working)
    func getActivityQualityForDateRange(from startDate: Date, to endDate: Date) -> Double {
        let stats = getActivityStatsForDateRange(from: startDate, to: endDate)

        let totalTime = stats.values.reduce(0, +)
        let workingTime = stats[.working] ?? 0

        guard totalTime > 0 else { return -1.0 } // Нет данных

        let quality = workingTime / totalTime
        print("🎯 StatisticsManager: Качество активности: \(Int(quality * 100))% (работа: \(Int(workingTime/60)) мин из \(Int(totalTime/60)) мин)")

        return quality
    }

    /// Возвращает статистику активности за сегодня
    func getActivityStatsForToday() -> [ActivityState: TimeInterval] {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        return getActivityStatsForDateRange(from: today, to: tomorrow)
    }

    /// Возвращает качество активности за сегодня
    func getActivityQualityForToday() -> Double {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        return getActivityQualityForDateRange(from: today, to: tomorrow)
    }

    // MARK: - НОВОЕ: Получение расширенной статистики отдыха (4.1)

    /// Возвращает статистику отдыха по типам за указанный период
    func getRestStatsByType(from startDate: Date, to endDate: Date) -> [RestType: (count: Int, totalDuration: TimeInterval)] {
        let restStats = getAllStoredRestStatistics().filter { stat in
            stat.date >= startDate && stat.date < endDate
        }

        var typeStats: [RestType: (count: Int, totalDuration: TimeInterval)] = [:]

        for stat in restStats {
            let current = typeStats[stat.type] ?? (count: 0, totalDuration: 0)
            typeStats[stat.type] = (count: current.count + 1, totalDuration: current.totalDuration + stat.duration)
        }

        print("🌿 StatisticsManager: Статистика отдыха по типам за период:")
        for (type, stats) in typeStats {
            print("🌿   \(type): \(stats.count) раз, \(Int(stats.totalDuration/60)) мин")
        }

        return typeStats
    }

    /// Возвращает статистику отдыха по качеству за указанный период
    func getRestStatsByQuality(from startDate: Date, to endDate: Date) -> [RestQuality: Int] {
        let restStats = getAllStoredRestStatistics().filter { stat in
            stat.date >= startDate && stat.date < endDate
        }

        var qualityStats: [RestQuality: Int] = [:]

        for stat in restStats {
            qualityStats[stat.quality, default: 0] += 1
        }

        print("🌿 StatisticsManager: Статистика отдыха по качеству за период:")
        for (quality, count) in qualityStats {
            print("🌿   \(quality): \(count) раз")
        }

        return qualityStats
    }

    /// Возвращает общее качество отдыха за период (процент хороших отдыхов)
    func getOverallRestQuality(from startDate: Date, to endDate: Date) -> Double {
        let qualityStats = getRestStatsByQuality(from: startDate, to: endDate)

        let totalRests = qualityStats.values.reduce(0, +)
        guard totalRests > 0 else { return -1.0 } // Нет данных

        let goodRests = (qualityStats[.excellent] ?? 0) + (qualityStats[.good] ?? 0)
        let quality = Double(goodRests) / Double(totalRests)

        print("🌿 StatisticsManager: Общее качество отдыха: \(Int(quality * 100))% (\(goodRests) хороших из \(totalRests))")

        return quality
    }

    /// Возвращает статистику отдыха за сегодня
    func getRestStatsForToday() -> [RestType: (count: Int, totalDuration: TimeInterval)] {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        return getRestStatsByType(from: today, to: tomorrow)
    }

    /// Возвращает качество отдыха за сегодня
    func getRestQualityForToday() -> Double {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        return getOverallRestQuality(from: today, to: tomorrow)
    }

    /// Возвращает топ проектов по количеству интервалов
    func getTopProjects(limit: Int = 5) -> [(UUID, Int)] {
        let projectStats = getProjectStatistics()
        return projectStats.sorted { $0.value > $1.value }.prefix(limit).map { ($0.key, $0.value) }
    }

    // MARK: - Work/Personal Statistics

    /// Возвращает статистику по рабочим и личным проектам
    func getWorkPersonalStatistics(projectManager: ProjectManager, from startDate: Date, to endDate: Date) -> (work: Int, personal: Int) {
        let intervals = getAllStoredIntervals().filter { interval in
            interval.date >= startDate && interval.date < endDate
        }

        var workIntervals = 0
        var personalIntervals = 0

        for interval in intervals {
            guard let projectId = interval.projectId,
                  let project = projectManager.getProject(by: projectId) else {
                // Интервалы без проекта считаем рабочими (для обратной совместимости)
                workIntervals += 1
                continue
            }

            if project.isWorkRelated {
                workIntervals += 1
            } else {
                personalIntervals += 1
            }
        }

        return (work: workIntervals, personal: personalIntervals)
    }

    /// Возвращает статистику по рабочим и личным проектам за сегодня
    func getWorkPersonalStatisticsToday(projectManager: ProjectManager) -> (work: Int, personal: Int) {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        return getWorkPersonalStatistics(projectManager: projectManager, from: today, to: tomorrow)
    }

    /// Возвращает статистику по рабочим и личным проектам за текущую неделю
    func getWorkPersonalStatisticsCurrentWeek(projectManager: ProjectManager) -> (work: Int, personal: Int) {
        let calendar = Calendar.current
        let now = Date()

        let weekday = calendar.component(.weekday, from: now)
        let daysFromMonday = (weekday == 1) ? 6 : weekday - 2
        let startOfWeek = calendar.date(byAdding: .day, value: -daysFromMonday, to: calendar.startOfDay(for: now))!
        let endOfWeek = calendar.date(byAdding: .day, value: 7, to: startOfWeek)!

        return getWorkPersonalStatistics(projectManager: projectManager, from: startOfWeek, to: endOfWeek)
    }

    /// Возвращает статистику по рабочим и личным проектам за текущий месяц
    func getWorkPersonalStatisticsCurrentMonth(projectManager: ProjectManager) -> (work: Int, personal: Int) {
        let calendar = Calendar.current
        let now = Date()

        let startOfMonth = calendar.dateInterval(of: .month, for: now)!.start
        let endOfMonth = calendar.dateInterval(of: .month, for: now)!.end

        return getWorkPersonalStatistics(projectManager: projectManager, from: startOfMonth, to: endOfMonth)
    }

    /// Возвращает детальную статистику по рабочим проектам
    func getWorkProjectsStatistics(projectManager: ProjectManager) -> [UUID: Int] {
        let intervals = getAllStoredIntervals()
        var workProjectStats: [UUID: Int] = [:]

        for interval in intervals {
            guard let projectId = interval.projectId,
                  let project = projectManager.getProject(by: projectId),
                  project.isWorkRelated else { continue }

            workProjectStats[projectId, default: 0] += 1
        }

        return workProjectStats
    }

    /// Возвращает детальную статистику по личным проектам
    func getPersonalProjectsStatistics(projectManager: ProjectManager) -> [UUID: Int] {
        let intervals = getAllStoredIntervals()
        var personalProjectStats: [UUID: Int] = [:]

        for interval in intervals {
            guard let projectId = interval.projectId,
                  let project = projectManager.getProject(by: projectId),
                  !project.isWorkRelated else { continue }

            personalProjectStats[projectId, default: 0] += 1
        }

        return personalProjectStats
    }
    
    // MARK: - Приватные методы

    func getAllStoredIntervals() -> [CompletedInterval] {
        guard let data = userDefaults.data(forKey: completedIntervalsKey) else {
            return []
        }
        
        do {
            let intervals = try JSONDecoder().decode([CompletedInterval].self, from: data)
            return intervals.sorted { $0.date < $1.date }
        } catch {
            print("❌ StatisticsManager: Ошибка декодирования интервалов: \(error)")
            return []
        }
    }
    
    func saveIntervals(_ intervals: [CompletedInterval]) {
        do {
            let data = try JSONEncoder().encode(intervals)
            userDefaults.set(data, forKey: completedIntervalsKey)
        } catch {
            print("❌ StatisticsManager: Ошибка сохранения интервалов: \(error)")
        }
    }

    // MARK: - Break Data Management

    /// Очищает все данные отдыхов (для тестирования)
    func clearAllBreaks() {
        userDefaults.removeObject(forKey: completedBreaksKey)
        print("🧹 StatisticsManager: Все данные отдыхов очищены")
    }



    func getAllStoredBreaks() -> [CompletedBreak] {
        guard let data = userDefaults.data(forKey: completedBreaksKey) else {
            return []
        }

        do {
            let breaks = try JSONDecoder().decode([CompletedBreak].self, from: data)
            return breaks.sorted { $0.date < $1.date }
        } catch {
            print("❌ StatisticsManager: Ошибка декодирования отдыхов: \(error)")
            return []
        }
    }

    func saveBreaks(_ breaks: [CompletedBreak]) {
        do {
            let data = try JSONEncoder().encode(breaks)
            userDefaults.set(data, forKey: completedBreaksKey)
        } catch {
            print("❌ StatisticsManager: Ошибка сохранения отдыхов: \(error)")
        }
    }

    // MARK: - Data Migration

    /// Назначает проект для интервалов без проекта (миграция старых данных)
    func assignProjectToLegacyIntervals(_ projectId: UUID) {
        var intervals = getAllStoredIntervals()
        var updatedCount = 0

        for i in 0..<intervals.count {
            if intervals[i].projectId == nil {
                intervals[i] = CompletedInterval(
                    date: intervals[i].date,
                    duration: intervals[i].duration,
                    projectId: projectId
                )
                updatedCount += 1
            }
        }

        if updatedCount > 0 {
            saveIntervals(intervals)
            print("📊 StatisticsManager: Назначен проект для \(updatedCount) старых интервалов")
        }
    }

    /// Возвращает количество интервалов без проекта
    func getLegacyIntervalsCount() -> Int {
        return getAllStoredIntervals().filter { $0.projectId == nil }.count
    }

    // MARK: - Очистка данных

    func clearAllIntervals() {
        userDefaults.removeObject(forKey: completedIntervalsKey)
        print("📊 Все интервалы очищены из UserDefaults")
    }

    // MARK: - Computer Time Tracking

    /// Записывает активную минуту
    func recordActiveMinute() {
        let now = Date()
        let calendar = Calendar.current
        let dayStart = calendar.startOfDay(for: now)

        var computerTime = getAllComputerTime()

        // Ищем запись для сегодняшнего дня
        if let index = computerTime.firstIndex(where: { calendar.isDate($0.date, inSameDayAs: dayStart) }) {
            // Увеличиваем счетчик активных минут для сегодня
            computerTime[index] = ComputerTime(date: dayStart, activeMinutes: computerTime[index].activeMinutes + 1)
        } else {
            // Создаем новую запись для сегодня
            computerTime.append(ComputerTime(date: dayStart, activeMinutes: 1))
        }

        saveComputerTime(computerTime)
        print("💻 StatisticsManager: Записана активная минута. Всего сегодня: \(computerTime.first(where: { calendar.isDate($0.date, inSameDayAs: dayStart) })?.activeMinutes ?? 0)")
    }

    /// Добавляет тестовые данные для демонстрации (временно)
    func addTestComputerTimeForDemo() {
        let now = Date()
        let calendar = Calendar.current
        let dayStart = calendar.startOfDay(for: now)

        var computerTime = getAllComputerTime()

        // Добавляем 120 минут (2 часа) для сегодня
        if let index = computerTime.firstIndex(where: { calendar.isDate($0.date, inSameDayAs: dayStart) }) {
            computerTime[index] = ComputerTime(date: dayStart, activeMinutes: computerTime[index].activeMinutes + 120)
        } else {
            computerTime.append(ComputerTime(date: dayStart, activeMinutes: 120))
        }

        saveComputerTime(computerTime)
        print("💻 StatisticsManager: Добавлены тестовые данные - 120 минут за сегодня")
    }

    /// Получает общее время за компьютером за сегодня в минутах
    func getComputerTimeForToday() -> Int {
        let today = Calendar.current.startOfDay(for: Date())
        let computerTime = getAllComputerTime()

        if let todayRecord = computerTime.first(where: { Calendar.current.isDate($0.date, inSameDayAs: today) }) {
            return todayRecord.activeMinutes
        }

        return 0
    }

    /// Получает общее время за компьютером за указанный период в минутах
    func getComputerTimeForDateRange(from startDate: Date, to endDate: Date) -> Int {
        let computerTime = getAllComputerTime()

        return computerTime
            .filter { $0.date >= startDate && $0.date < endDate }
            .reduce(0) { $0 + $1.activeMinutes }
    }

    /// Получает время за компьютером по дням для указанного периода
    func getComputerTimeByDays(from startDate: Date, to endDate: Date) -> [ComputerTime] {
        let computerTime = getAllComputerTime()

        return computerTime
            .filter { $0.date >= startDate && $0.date < endDate }
            .sorted { $0.date < $1.date }
    }

    /// Очищает все данные времени за компьютером (для тестирования)
    func clearAllComputerTime() {
        userDefaults.removeObject(forKey: computerTimeKey)
        print("💻 Все данные времени за компьютером очищены")
    }

    // MARK: - НОВОЕ: Управление данными активности (4-бандовая система)

    /// Получает все сохраненные записи активности
    func getAllStoredActivityRecords() -> [ActivityRecord] {
        guard let data = userDefaults.data(forKey: activityRecordsKey) else {
            return []
        }

        do {
            let records = try JSONDecoder().decode([ActivityRecord].self, from: data)
            return records.sorted { $0.date < $1.date }
        } catch {
            print("❌ StatisticsManager: Ошибка декодирования записей активности: \(error)")
            return []
        }
    }

    /// Сохраняет записи активности
    func saveActivityRecords(_ records: [ActivityRecord]) {
        do {
            let data = try JSONEncoder().encode(records)
            userDefaults.set(data, forKey: activityRecordsKey)
        } catch {
            print("❌ StatisticsManager: Ошибка сохранения записей активности: \(error)")
        }
    }

    /// Очищает все данные активности (для тестирования)
    func clearAllActivityRecords() {
        userDefaults.removeObject(forKey: activityRecordsKey)
        print("🎯 StatisticsManager: Все данные активности очищены")
    }

    // MARK: - НОВОЕ: Управление данными расширенной статистики отдыха (4.1)

    /// Возвращает все сохраненные записи расширенной статистики отдыха
    func getAllStoredRestStatistics() -> [RestStatistics] {
        guard let data = userDefaults.data(forKey: restStatisticsKey) else {
            return []
        }

        do {
            return try JSONDecoder().decode([RestStatistics].self, from: data)
        } catch {
            print("❌ StatisticsManager: Ошибка декодирования статистики отдыха: \(error)")
            return []
        }
    }

    /// Сохраняет записи расширенной статистики отдыха
    func saveRestStatistics(_ restStats: [RestStatistics]) {
        do {
            let data = try JSONEncoder().encode(restStats)
            userDefaults.set(data, forKey: restStatisticsKey)
        } catch {
            print("❌ StatisticsManager: Ошибка сохранения статистики отдыха: \(error)")
        }
    }

    /// Очищает все данные расширенной статистики отдыха (для тестирования)
    func clearAllRestStatistics() {
        userDefaults.removeObject(forKey: restStatisticsKey)
        print("🌿 StatisticsManager: Все данные расширенной статистики отдыха очищены")
    }

    /// Добавляет тестовые данные активности
    func addTestActivityData() {
        let calendar = Calendar.current
        let now = Date()

        // Добавляем записи активности за последние несколько дней
        for dayOffset in 1...7 {
            guard let workDay = calendar.date(byAdding: .day, value: -dayOffset, to: now) else { continue }

            // Симулируем рабочий день с разными состояниями
            let states: [ActivityState] = [.working, .awayShort, .working, .awayMedium, .working, .awayShort]
            let durations: [TimeInterval] = [3600, 300, 2400, 900, 1800, 600] // в секундах

            for (index, state) in states.enumerated() {
                let stateTime = calendar.date(byAdding: .hour, value: 9 + index, to: workDay) ?? workDay
                let duration = durations[index]

                // Создаем случайные данные бандов для демонстрации
                let bandData = (0..<4).map { _ in Bool.random() }

                recordActivityState(state: state, duration: duration, bandData: bandData)
            }
        }

        print("🎯 StatisticsManager: Добавлены тестовые данные активности")
    }

    /// Добавляет тестовые данные расширенной статистики отдыха
    func addTestRestStatistics() {
        let calendar = Calendar.current
        let now = Date()

        // Добавляем записи отдыха за последние несколько дней
        for dayOffset in 1...7 {
            guard let workDay = calendar.date(byAdding: .day, value: -dayOffset, to: now) else { continue }

            // Симулируем разные типы отдыха в течение дня
            let restScenarios: [(RestType, RestQuality, RestContext, TimeInterval)] = [
                (.micro, .good, .informalSession, 90),        // Микропауза 1.5 мин
                (.partial, .good, .informalSession, 480),     // Частичный отдых 8 мин с продолжением
                (.partial, .poor, .informalSession, 360),     // Частичный отдых 6 мин с прерыванием
                (.partial, .fair, .formalInterval, 900),      // Прерванный отдых 15 мин
                (.automatic, .excellent, .systemDetected, 1200), // Полный автоматический отдых 20 мин
                (.formal, .excellent, .formalInterval, 300)    // Формальный отдых 5 мин
            ]

            for (index, scenario) in restScenarios.enumerated() {
                let restTime = calendar.date(byAdding: .hour, value: 10 + index * 2, to: workDay) ?? workDay
                let (type, quality, context, duration) = scenario

                // Случайная активность за компьютером для некоторых отдыхов
                let wasComputerActive = Bool.random()
                let computerActiveTime = wasComputerActive ? TimeInterval.random(in: 30...120) : 0

                // Случайные данные бандов
                let bandData = (0..<4).map { _ in Bool.random() }

                recordRestStatistics(
                    duration: duration,
                    type: type,
                    quality: quality,
                    context: context,
                    wasComputerActive: wasComputerActive,
                    computerActiveTime: computerActiveTime,
                    bandData: bandData
                )
            }
        }

        print("🌿 StatisticsManager: Добавлены тестовые данные расширенной статистики отдыха")
    }

    // MARK: - Тестовые данные

    func addTestData() {
        let calendar = Calendar.current
        let now = Date()

        // Добавляем интервалы за последние несколько дней
        for dayOffset in 1...7 {
            guard let workDay = calendar.date(byAdding: .day, value: -dayOffset, to: now) else { continue }

            // Случайное количество интервалов в день (2-6)
            let intervalsPerDay = Int.random(in: 2...6)

            for intervalIndex in 0..<intervalsPerDay {
                // Начинаем работу в 9 утра + случайное время
                let startHour = 9 + Int.random(in: 0...8)
                let startMinute = intervalIndex * 60 + Int.random(in: 0...30)

                if let intervalTime = calendar.date(bySettingHour: startHour, minute: startMinute, second: 0, of: workDay) {
                    // Создаем интервал с нужной датой
                    let interval = CompletedInterval(date: intervalTime, duration: 3120) // 52 минуты
                    var intervals = getAllStoredIntervals()
                    intervals.append(interval)
                    saveIntervals(intervals)
                }
            }
        }

        print("📊 Добавлены тестовые данные для демонстрации")
    }



    // MARK: - Computer Time Data Management

    private func getAllComputerTime() -> [ComputerTime] {
        guard let data = userDefaults.data(forKey: computerTimeKey) else {
            return []
        }

        do {
            let computerTime = try JSONDecoder().decode([ComputerTime].self, from: data)
            return computerTime.sorted { $0.date < $1.date }
        } catch {
            print("❌ StatisticsManager: Ошибка декодирования времени за компьютером: \(error)")
            return []
        }
    }

    private func saveComputerTime(_ computerTime: [ComputerTime]) {
        do {
            let data = try JSONEncoder().encode(computerTime)
            userDefaults.set(data, forKey: computerTimeKey)
        } catch {
            print("❌ StatisticsManager: Ошибка сохранения времени за компьютером: \(error)")
        }
    }
}

// MARK: - Модель данных

struct CompletedInterval: Codable {
    let date: Date
    let duration: TimeInterval
    let projectId: UUID?        // Опциональный для обратной совместимости
    let intervalType: String    // "formal" или "informal" - для различения типов интервалов

    init(date: Date, duration: TimeInterval, projectId: UUID? = nil, intervalType: String = "formal") {
        self.date = date
        self.duration = duration
        self.projectId = projectId
        self.intervalType = intervalType
    }
}

struct CompletedBreak: Codable {
    let date: Date
    let duration: TimeInterval
    let wasComputerActive: Bool
    let computerActiveTime: TimeInterval
    let qualityPercentage: Int  // Процент качества отдыха (0-100)

    init(date: Date, duration: TimeInterval, wasComputerActive: Bool, computerActiveTime: TimeInterval, qualityPercentage: Int = 100) {
        self.date = date
        self.duration = duration
        self.wasComputerActive = wasComputerActive
        self.computerActiveTime = computerActiveTime
        self.qualityPercentage = qualityPercentage
    }
}

struct ComputerTime: Codable {
    let date: Date          // Дата (начало дня)
    let activeMinutes: Int  // Количество активных минут за день

    init(date: Date, activeMinutes: Int) {
        self.date = date
        self.activeMinutes = activeMinutes
    }
}

// НОВОЕ: Структура для записи активности (20-отрезковая система с погрешностью)
struct ActivityRecord: Codable {
    let date: Date              // Время записи
    let state: ActivityState    // Состояние активности
    let duration: TimeInterval  // Продолжительность в этом состоянии (секунды)
    let bandData: [Bool]        // Данные отрезков активности (опционально)

    init(date: Date, state: ActivityState, duration: TimeInterval, bandData: [Bool] = []) {
        self.date = date
        self.state = state
        self.duration = duration
        self.bandData = bandData
    }
}

// Enum для состояний активности (должен быть Codable)
enum ActivityState: String, Codable, CaseIterable {
    case working = "working"
    case awayShort = "awayShort"
    case awayMedium = "awayMedium"
    case awayLong = "awayLong"
    case awayVeryLong = "awayVeryLong"
    case formalRest = "formalRest"
}

// НОВОЕ: Структуры для расширенной статистики отдыха (4.1)
enum RestType: String, Codable, CaseIterable {
    case formal = "formal"           // Формальный отдых (по таймеру)
    case automatic = "automatic"     // Автоматический отдых (17+ мин неактивности)
    case partial = "partial"         // Частичный отдых (2-17 мин)
    case micro = "micro"            // Микропауза (0-2 мин)
}

enum RestQuality: String, Codable, CaseIterable {
    case excellent = "excellent"     // Полный отдых 17+ мин
    case good = "good"              // Частичный с продолжением отдыха
    case fair = "fair"              // Прерванный отдых (10-17 мин → работа)
    case poor = "poor"              // Частичный с прерыванием (2-10 мин → работа)
    case interrupted = "interrupted" // Отдых прерван пользователем
}

enum RestContext: String, Codable, CaseIterable {
    case formalInterval = "formalInterval"     // Во время формального интервала
    case informalSession = "informalSession"   // Во время неформальной сессии
    case betweenSessions = "betweenSessions"   // Между сессиями
    case systemDetected = "systemDetected"    // Обнаружено системой автоматически
}

// Расширенная структура статистики отдыха
struct RestStatistics: Codable {
    let date: Date                  // Время начала отдыха
    let duration: TimeInterval      // Продолжительность отдыха
    let type: RestType             // Тип отдыха
    let quality: RestQuality       // Качество отдыха
    let context: RestContext       // Контекст отдыха
    let wasComputerActive: Bool    // Была ли активность за компьютером
    let computerActiveTime: TimeInterval // Время активности за компьютером
    let bandData: [Bool]           // Данные отрезков активности (опционально)

    init(date: Date, duration: TimeInterval, type: RestType, quality: RestQuality, context: RestContext, wasComputerActive: Bool = false, computerActiveTime: TimeInterval = 0, bandData: [Bool] = []) {
        self.date = date
        self.duration = duration
        self.type = type
        self.quality = quality
        self.context = context
        self.wasComputerActive = wasComputerActive
        self.computerActiveTime = computerActiveTime
        self.bandData = bandData
    }
}
