
# uProd - Productivity Timer

## Последняя версия на GIT и в OS
**APP_VERSION = 0.2.8.2.08**

---

## Как работать с LLM?
`/AI-memories/cooperate_with_AI.md`  

# BUGS 🐞
- [ ] Неформальные интервалы работают, но системы эксалации в них - нет. Нажал "I need a couple of minutes" и продолжил работу спокойно. Никаких желтых зон не увидел. 

- [ ] После завершения отдыха - что если я не хочу работать? Например вечер, а он прям настаивает
    - В первую очередь сделать завершение дня. 
- [ ] На странице проектов есть проблема, если проектов мало, то они выравниваются по нижнему краю. Если мы эту проблему исправим, то если проектов много, то они выравниваются по нижнему краю скрола. То есть вот у нас, например, 6 проектов и как будто скрол вниз. Надо решить эти обе проблемы, чтобы были решены.
- [x] логи. Постоянно что-то не работает. какие-то они неполноценные. `добавь в память`
    ✅ logInfo() сообщения появляются в логах
    ❌ print() сообщения НЕ появляются в логах
    Система использует функции logInfo(), logDebug(), logError() и т.д. из файла Logger.swift.Заменим все print() в ActivityStateManager на logInfo():

# TEST
- Сделать тесты для каждого ключевого функции (для каждого окна, и пр)

# 0.1 - ✅
- [x] Timer
- [x] Base stats 
    - [x] weeks
    - [x] days
- [x] Projects
    - [x] fix  "create projects"
    - [x] Нужно добавить возможность удаления проекта, а то кнопка как будто не активна кнопка удаления.
    - [x] Возможность редактирования проекта
    - [x] Убрать "Выбрать" 
        и сделать чтобы выбрать проект можно было по нажатию в любом месте. Но для чего вообще выбирать? Наверное по клику в любом месте будет открываься окно редактирования. 
    - [x] Шапка 
        Дальше нужно заголовок поправить. У нас заголовок слишком высоко уехал, нужно его поместить на уровень кнопки создать проект. Заголовок управления проектами нужно поместить на уровень кнопки создать проект. А еще лучше чуть-чуть кнопку создать проект приподнять и на этот уровень поместить заголовок управления проекта.
    - [x] Краткая инфа о проекте другая и должна отражать реальные данные (часов, дата старта проект завершен? (статус преокта))
    - [x] Фикс: выравнивание проектов не по низу окна с проектами, а по верху. Странно почему по умолчанию по низу выровняось. 
    - [x] Компактное расположение элементов внутри блока. И не по вертикали а по горизонтали.  
    - [x] Избранные проекты наверху над неизбранными. При добавлении в избранное у он сразу перемещается наверх.
    - [x] Перемещение проектов. Перетаскиванием. И это будет влиять на порядок в контекстном меню. Если в избранном на первом, о и в контекстном меню на первом 
        - [ ] Так, еще я заметил баг в перетаскивании проектов. То есть, если надо переместить какой-то проект на первое место, то вот эта полоска, куда надо переместить, она только между проектами. А вот между, точнее, перед первым или после последнего такой полоски нет и вообще непонятно, как туда переместить. Вот мне надо, например, третье на первое место переместить. А я этого не могу, мне полоски не получается и без полоски перемещение не работает.
    - [x] рабочие vs личные? 
        Надо подумать отдельно. Чтобы потом в статистике можно было показать "только рабочие" 
        Может потом просто сделать возможность редактировать списки. Учеба, хз что им надо, наука, хобби
    - [х] Сделать проект "без категории" - пока вручную
    - [x] В смене названия проекта это не учитывается в контекстном меню, в контекстном меню также старое название. Надо, чтобы после смены названия моментально это отображалось в контекстном меню.
    - [x] То же самое с приоритетом, но почти то же самое. То есть после обновления перетаскивания, перетаскивание порядок не меняется моментально после перетаскивания. В конце концов на меню порядок старый. Не знаю, что нужно сделать, чтобы моментально порядок менялся в контекстном меню
    - [x] Не работает установление иконки, то есть я хочу например поменять иконку, как ее задать. Я не могу, я ввожу стандартные глобовые е, у меня просто е показывает, то есть я не могу иконку эмоций выбрать. Я могу конечно из буфера ее вставить, но это по-моему не выход.
    - [x] В контекстном меню иконка проекта приоритет (если есть). А не иконка категории. И только если нет иконки проекта - показываем иконку категории. 
- [x] Сделать блок для идей по результатам тестирования
- [x] Нормальное отображение статистических показателей 
    - [x] Среднее в день. Там если 0.3, то он показывает 3. 
    - [x] Время старта тоже показывает без ":"
- [x] Убрать анимацию кнопок из попапа
- [x] Порядок в контесктном меню
    - [x] Удалить 3 пункта связанных с "Демо-данные" из контекстного меню
    - [x] Убрать "другие проекты"
    - [x] Убрать "демо новое окно завершения"
    - [x] ПЕРЕМЕСТИТЬ "тестовый запуск"  в самый низ, над "выход". И обособить разделителем сверху
    - [x] Добавите emoji для всех элементов меню контекстного. му, где их еще нет. То есть настройки, статистика, управление проектами и тестовый запуск. И выход.
- [x] Проекты в окне статистики. В той области где выбор перида. После выбора дня, недели, месяца. В том же стиле. 
    По-умолчанию "Все". При нажатии в окне можно выбрать галочками проекты и типы (рабочий, личный и пр)
- [x] Коммит
# 0.2 - ▶️ [OK FOR ME]`по результатам использования`
- [x] `0.2.1` Учет отдыха 
    После начала отдыха (после нажатия кнопки "take a break") сделать таймер отдыха (17 минут или 90 минут после 4 интервалов) - а то многие нажмут "take a break" а сами работают. Или сидят за компом. Это не отых. Таймер меняет цвет на приятный зеленый, но не кислотный. А иконка вместо помодоро превращается в смайл отдыхающего emoji, или recovery. Или полная батарейка. Или листики растения на ветру. 🍃 ⏱️ 🔋 ⏳ 🧘🏼‍♂️ 🔄
    - [x] И напоминать закрыть ноутбук и идти гулять (если он сидит за компом и что-то делает). Например через каждую минуту. 
    - [x] И можно отслеживать был ли открыт ноут во время отдыха, были ли какие-то действия и отображать потом в статистике и в рекомендациях. В расширеной статистике. Если 90% времени он провел вне ноута, то это ок. 
    - [x] Не 15 > 17 минут. После 4 интервалов отдых 90 минут. Но по запросу. Спрашиваем сколько отжохнуть. Коротких или длинный брейк. Можно уже начиная с 3-го спрашивать.
    - [x] Баг? Кстати, когда идет выбор Long или Short, там как будто бы таймер переработки замирает на 0:01. Это баг. Пока человек не определился, время переработки увеличивается. 
    - [x] Кнопки снизу. Не меняя ширины кнопок. Текст сверху. Только 1 фраза. Без кол-ва интервалов. И в конце высоту уменьшить, КАК  ОРИГИНАЛЬНОМ ОКНЕ СДЕЛАТЬ. 
    - [x] `надо тестить` Теперь давай вернем обратно показывать этот выбор только после третьего интервала. И у меня вот вопрос, а если он использовал большой интервал после третьего интервала, то ему в четвертом интервале уже не будет это показываться, правильно? Или начиная с третьего, каждый раз будет показываться? Даже несмотря на то, что он уже выбрал длинный интервал. 
    - [x] Как отслеживать статистику того как он отдыхал? Провоерять каждую минуту был ли активен? И считать в процентах в итогах отдыха? Причем надо учитывать длинный интервал. То есть его тоже как-то надо считать в таком же ключе, чтобы он также считался как и короткий. Ну в процентах, наверное, я думаю. То есть 100% от длинного, 100% от короткого.
        - [x] Добавить расширенную статистику в экан статистики. У нас уже есть окно статистики со статистическими элементами, там их 6 блоков или сколько-то. Может быть мы сделаем такой стрелочку вниз или как-то я не знаю, посередине или кнопочку, со стрелочкой, типа показать все и будут показаны еще дополнительно и вот в данном случае вот этот один, то есть, ну то есть, я не знаю как это 100% отдых или как, то есть, у отдыха время интервал или как, не знаю, надо подумать, как назвать его, вот и его надо как-то так поместить, чтобы у него ширина была не на всю длину этого окна, да, как вот как-то вот максимальную ширину что ли ему задать, я не знаю, надо подумать.
        - [x] В логическом блоке данные обновляются только после завершения отдыха, а не так, что он даже ни одного отдыха не начал, а у него уже показывает среднее качество отдыха 100% Или там он начал, например отдыхать, одну минуту отдохну, у него уже показывает там 100% То есть считается только после завершения сессии отдыха.
    - [x] После истечения таймера отдыха не хватает окна "начать интервал?" с выбранным последним проектом. И возможностью проект изменить в 1-2 клика. Ты же это сделал? Можно потестить?
        - [x] При нажатии на проект надо сделать возможность выбрать проект. Сейчас у нас тупо открывается страницы проектов. А надо создать какое-то просто диалоговое окно, чтобы в один клик можно было выбрать проект для следующего интервала.
        - [x] Что происходит после нажатия "later"? Через 5 или 15 минут? А может справшивать? 5 или 15 минут? Или это слишком навязчиво? Давай для начала установим просто на 10 минут, жестко запрограммируем, а потом, если надо, я все это сделаю более сложную логику, потому что сейчас на этом останавливаться я прям не очень не хочу.
    - [x] Поправить зеленое окно напоминания во время отдыха, если есть активность. надо и таймер сделать больше, и пониже его опустить. И с кнопками что-то решить. "понятно" "пропустить", сделать другими. Надо чо-то одно наверное оставить. И само окно меньше по высоте сделать. И там же еще есть текст "обнаружена активность". Его тожде надо куда-то поместить повыше.  
        - [x] функционал кнопок
    - [x] 113 отдыхов - нужно учитывать только полноценные отдыхи. Тестовые не в счет. 
    - [x] билд и коммит
- [x] `0.2.2` Удаление "🎨 Тест окна отдыха" в контекстном меню
- [x] `0.2.3` Настройки - дизайн и добавление пунктов (пока только дизайн)
- [x] `0.2.4` Общее время за компом && Звуки && Анимация тряски && Версия
- [x] `0.2.5` Логи & Команда сборки ./build.sh 
    - [x] '1. Кнопка настроек на экране статистики'
    - [x] '2. Неформальные интервалы'
        - [x] Позиционирование yellow-zone
        - [x] Дубли окна в формальном окне
        - [x] Анимацию тряски только для окон, которые не скрыты  
        - [x] Что если я ушел гулять? На жетой зоне. Ничего не нажимая. Бужет ли показана сообщение оранжевой? Для формального и неформального.   
- [x] `0.2.6` Система тестирования TESTING.md
    - [x] '2. Описание концепции кайдзен'
    - [ ] '3. '
- [x] `0.2.7` Унифицированная система интервалов + новая система обнаружения активности
    - [x] Унифицированная система интервалов
        - [x] Поправить кнопку на полноэкранном критическом 
        - [x] Счетчик 10...3,2,1 до блокировки экрана и последнего уведомления
        - [ ] Менеджер звуков в унифицированной системе или нет? Должен быть только для формальных? 
        - [ ] Изменить тайминги эскалации
    - [x] Унифицированная система активности
        - [x] Обсудить концепцию и создать описание
        - Детали
            - [x] Учитываются ли нажатия клавиатуры в активности? Или только мышь? 
            - [x] Для обнаружения выхода из отдыха мне кажется 15 секундного бандла как-то недостаточно и слишком поздно. Представь человек пошевелился, а ему через 15 секунд только говорится типа "ну отдохни, он уже там не знаю открыл что-то, надо как-то максимум секунды через 3". Может быть сделаем, я не знаю, ну короче надо подумать, в принципе это решается.
            - [x] Но и от случайной активности тоже надо как-то предостеречь, что там мало ли кошка пробежала, там мышку задели, там кто-то убирался, мышку задели. И такое не должно учитываться. Только в течение там 3 секунд, например, все три должны быть активны. Можно мини-бандлы тоже сделать, еще один. Там бандлы по 3 секунды из 3 секунды стоящие, не знаю.
            - [x] убрать ненужные тесты из сборки (test.sh), чтобы время не тратить каждый раз но и риска регрессии избежать
            - [x] Так, вот у нас, если вот он ушел, например, то есть ему высохлилось сообщение "session completed", и он ушел сразу. Через сколько вот это окно будет скрыто автоматически? Через 10 минут? Через сколько?
            - [x] Также непонятно, когда убирать вот эти вот окна, не только в оттекущее окно, но у нас может быть там еллзон уже висит или что там висит, я не знаю.
            - [x]И  вот еще при тестах, вот я например попробовал, ушел на 15 секунд, потом вернулся на 6, потом на 10, потом опять ушел на 20 секунд. И сколько вот так можно надрачивать. Или вот эти вот у нас же есть планки, до двух минут, то есть это все не засчитывается. А эти вот минуты, с какого момента начинаются? Или они гибкие, растягивать их можно бесконечно? Или они как так тайны? И они вот жестко идут параллельно, то есть первые две минуты, что это считается?
            - [x] Не будет ли конфликта показа окон когда он вернется после 20 минут отсутствия? С однй остороны ему покажется что отдых завершен, начинай работу. А с другой - окно возвращения, где есть "вы отдыхали уже 20 минут" и кнопки "keep resting" and "start session". Или это окно показывается только максимум 15 минут? 
            - [x] Кнопка Keep Resting, наверное, ну вот это вот окно даже отдыха при движении будет появляться только, наверное, до 15 минут, потому что ну а смысл если оно появляется 15 минут человек отдохнул, и мы к нему будем придираться, что-то идти еще типа две минуты отдыхай, но это уже слишком
            - [x]Я нажимаю кнопку Take a Rest на последнем этапе эскалации. Ну, может и раньше это тоже не работает, но у меня приложение закрывается с крашем.
            - Вопрос по окну "вы отдыхали 2 минуты". Допустим я нажму. Что происходит по "keep resting"? Он же не нажимал "tfke a rest", а система автоматически его определелила. Отдых же не офоциальный, а как бы скИ какие есть варианты? Запустить официальный отдых? Но надо тогда как-то вычесть из времени на отдых то что уже было отдохнуто. Или просто ничего не делать и пусть он уходит? Если будет неактивность, то это будет учитываться как отдых. Наверное первый вариант, потому что раз нажимает, значит хочет видеть таймер отдыха. 
                - [ ] Исправлено, надо тестить
            - [x] Я нажал "take a break" и начал отдых. Идет таймер. Но я продолжаю сидеть за компом. То есть есть активность. Раньше это обнаруживалось и вылезало сообщение. А сейчас как будто нет. Правилньо ли состоялся переезд на новую систему обнаржуения активности для этого окна? 
            - [ ] У меня же есть сообщение, вы отдыхали там две минуты, допустим, если он просто ушел. А что если это сделать еще и для формального отдыха? То есть счетчик у него там идет, например, 14 минут отдыха осталось. И когда он приходит, ему как бы... Появляйте сообщение, вы отдыхали уже там 5 минут или там 15 минут. Чего сделать дальше, типа кипрестинг или... ...start new interval. То есть как-то так, да? Но надо сделать так, чтобы это с зеленым окном обнаружение активности и не конфликтовало.
            - [x] `BUG` Во время активного формального интервала, ну кстати, может и не формального, не знаю, я минуту не пользовался компьютером, я просто аудио записывал, то есть я имею в виду, не пользовался мышкой, там еще чем-то клавиатурой и мне высветилось сообщение, которого я вообще не ожидал увидеть, то есть он мне высветилось, вы отдыхали одну минуту, хотите продолжить отдых или вернуться к работе я вообще был в шоке, потому что у меня активный интервал идет, там 40 минут мне это осталось, то есть откуда, почему она вообще здесь высветилась, надо вообще, чтобы не было то есть откуда у меня сейчас не отдых, у меня же активный интервал, откуда вообще это вылезло причем надо не просто сообщение скрыть, а чтобы вообще вот этого, причину этого какого-то обнаружения отдыха или чего, то есть будет параллельная система отдыха, система правильного обнаружения отдыха, включиваясь или что я не понимаю, то есть какая логика вызвала про это окно у меня, то есть надо не сам окно отключить, а именно логику показывая этого окна и почему она здесь вызвалась, почему она стала у меня отслеживать отдых, хотя я работаю
            - [x] `BUG` Что делать если он во время рабочего интервала закроет крышку ноута? На 2-3 минуты (туалет), на 5-10 минут. Или больше?
                - ✅ **РЕАЛИЗОВАНО**: Трехуровневая система для закрытия крышки ноутбука:
                    - **0-3 минуты**: интервал продолжается (туалет, короткий перерыв)
                    - **3-10 минут**: интервал приостанавливается и автоматически возобновляется
                    - **10+ минут**: интервал сбрасывается полностью (длительное отсутствие)
                - ✅ Автоматическое возобновление после среднего сна
                - ✅ Логирование всех событий сна/пробуждения
            - [x] Что делать если он неактивен во время интервала? НИЧЕГО! Счеткчик должен идти. Надо уточнить так ли это сейчас! Хотя если он больше 10 минут неактивен, то таймер сбрасывается. Но с подтверждением. Если подтверждения нет, то сбрасывается через минуту.
                - ✅ **ИСПРАВЛЕНО**: Реализована разумная логика неактивности во время интервала:
                    - **0-10 минут**: таймер продолжает работать (человек может думать, читать, контролировать)
                    - **10-15 минут**: таймер приостанавливается (человек отошел ненадолго)
                    - **15+ минут**: интервал сбрасывается полностью (человек ушел надолго)
                - ✅ Убрана абсурдная логика остановки таймера через 15 секунд
                - ✅ Добавлен callback `onResetIntervalDueToInactivity` для сброса интервала
                - ✅ Логирование: "ИНТЕРВАЛ ПРИОСТАНОВЛЕН" и "ИНТЕРВАЛ СБРОШЕН"
            - [x] У меня появилось окно неформального интервала. Что типа "Time to rest", а потом, то есть во время формального интервала, как бы противоречия. А потом через минуту как раз у меня закончилось формальное интервал. И еще формальное интервал говорит типа "Иди отдыхай". Если запущен формальный интервал, то неформальные как бы не нужны. По-моему это очевидно.
            - [ ] По поводу последних пунктов может ты сделать такое же и для неформальных интервалов тоже. Как ты считаешь, то есть закрытие крышки вот это все, то есть брос вот эти все при отходе, там на три минуты там и так далее. Потому что какая разница по идее.
- [ ] `0.2.8` Система КАЙДЗЕН (база) 
    - Перед каждым пунктом нужно изучить всю документацию `/docs/_kaizen-system.md` и все вложенные документы для понимания контекста. И можно сдесь создать подпункты итоговые
    - [x] `1`Приоритетный проект (Ichiban) - фокус на одном главном проекте
        - [x] На странице проектов
            - [x] визуальнео выделение рамкой
            - [x] выделение иконкой "darts" (по аналогии с избранным)
            - [x] приоритетный проект всегда наверху (самый первый)
            - [x] не может быть 2 проекта. Если он пробует выбрать вторйо приоритетный проект, то в даилоговом окне будет подтверждение снятия приориттеного дл предыдущего.
        - [x] В настройках
            - [x] Во второй вкладке можно создать пункт "Приоритетный проект" и возможность выбрать из списка проектов. Не забыть отделить от предыдущих пунктов разделителем
    - [>] `2`Раннее вовлечение и адаптивные интервалы - мотивация начать работу над проектом утром
        - [x] Определить критерии и задачи на основе документации 
            - [x] Изучить п. 8 (примерно с 200 строки) в файле `/docs/sustainable-growth-system.md`
            - Подумайте о системах эскалации, мы их, наверное, по-другому назовем.
                - Первая - эскалация по дням. Что делать, если он там один день без проекта, второй день без проекта и какие сообщения выводить.
                - Вторая эскалация в течение дня, сколько ему вообще напоминаний присылать по проекту в течение дня утром, сколько, если он один раз ответил - нет, то через час будет ли там еще что-то? Ну как-то так, не будет ли это зависеть от первой эскалации, наверное чем меньше у него пауза по работе с проектом, я имею в виду в днях, тем чаще ему будут напоминания вот эти вот течение дня, приходить что-нибудь, давай, давай братан!
                - Можно попробовать немножко читерить такие умные первые сообщения внедрить. Сразу спрашивать какие-то вопросы, которые сразу будут в поток вводить. То есть понятно ли тебе что делать? Да, нет. Сразу как бы да, если нет, то давай прояснять, если да, то давай приступать соответственно. Например, это может быть еще что-то, какие-то другие сообщения. То есть система прояснения задач - это вообще отдельная задача (на 4-м этапе про барьеры будет), но какую-то болванку сделаем.
                - Так же да, там надо еще водный инструктаж для новых пользователей, особенно первые дни, что начинать нужно как можно раньше, почему и так далее.
                - `Крайне важно, капслоком, что надо внедрить систему план минимум`. Что типа, если он там не хочет ничего сегодня делать, у него должна какая-то планка быть, как вот дуалингу сделали, там две минуты каждый день. Но это моя уже разработка из Notion. Что делать хотя бы план минимум каждый день и дальше ты уже как бы, ну там 10 отжиманий или там одно отжимание, по-моему, да? Это надо в документацию вот этой системы. Впрочем, развитие сделать, подумать, как это все в систему внедрить. Это прям база-база. Я просто уже забыл про это, но это прям очень архиважно.
            - Здесь причем интервалы таять должны прям очень быстро, если он пропускает работу по важному проекту. Если вчера было 52, то он не сделал, то сегодня будет уже 30, если он 30 не сделал, то 15, если он 15 не сделал, то 5, если 5 не сделал, то хотя бы 1 минуту. Нарачивание будет медленным, а таяние будет очень быстрой. И вот эту мою главную идею надо реализовать. Если успех, то повышаем, если не успех, то понижаем. Это классная штука.
            - итоговый план здесь -  `docs/early-engagement-implementation-plan.md` 
        - [x] Реализовать весь план внедрения `docs/early-engagement-implementation-plan.md`
        - [>] Исправить баги и добиться работы
            - [x] Добиться нормальных определений планок
            - [x] Система взращивания сессии - `/docs/session-growing-system.md`
            - [x] системы полных планок `/docs/full-session-system.md`
            - [>] **Разобраться с кнопками** (как они формируются?)
                - [x] Собрать первичную инфу
                    - Сейчас просто тексты. Надо сделать логику, матрицу. Чтобы понятно было. Чтобы подставлялась планка типа "Начать [current_bar]" "Сделать [plan_minimum]" "Полная сессия [52 min]" "[later]" 
                    - у нас же есть система полной планка `docs/full-session-system.md`. Часть будет там. 
                    - Минимумы для последних 2-х уровней по горизонтали
                    - [ ] Сделать матрицу кнопок (предсказуемую) 
                        - описать
                        - добавить в отладочное окно (точнее сделать вывод в отладочном окне реального кода)
                    - Внедрить в отладочное окно. И спросить как быть увереным что это модульные кнопки из матрицы, а не просто тексты. 
                - [ ] Обсудить с LLM все что знаю. Задать вопросы. Написать промт. 
            - [ ] Разобраться с текстами
            - [ ] Дизайн окон
                - Подумать как объединить с существующими окнами формальный окон и где там еще
                - Учесть что кнопка полной сессии может быть скрыта
            - [ ] Вопросы
                Так, нашел еще одну проблему в логике, то есть у меня есть пример в файле Early Engagement Task. То есть ситуация разобрана на строках 155-171. И на строке 162 сказано, что вот пользователю было предложено сначала 29 минут поработать при первом предложении, он отказался. Потом через там сколько-то минут ему было сделано второе предложение поработать, соответственно, -50% это 15 минут. Пользователь согласился и завершил этот интервал. И потом шаг 3 обнуление, и он же завершил второй предложение, интервал завершен. Почему планка 29 минут зафиксирована? Он же за 15 минут согласился работать. То есть ты, наверное, не понял, что нужно фиксировать не то, что было первым предложением, а то, на что он согласился, вот на эту планку, наверное, он и, в принципе, его и надо, как бы... Хотя, опять же, не знаю, правильно ли это или нет, то есть он вроде бы там начался с какого-то минимума, но нет, это, скорее всего, неправильно, потому что вот он, что если он там в 4-м предложении за день поработает, там план минимум, да, 3 минуты, например, и что, ему теперь планку обновлять, что ли? Но не было у него сегодня настроения, он план минимум поработал, и что, ему теперь планки что ли обновлять, блин, тоже как-то неправильно, да? В принципе, он поработал 3 минуты, значит, планка сохраняется, все нормально. Или как лучше сделать, как ты считаешь? Давай подумаем.
        - [ ] Реализовать статистику "сколько дней уже не работал над ключевым проектом" 
        - [x] Детектор пробуждения
    - [ ] `3` Работа с барьерами - помощь при лени, неясности задач
        - [ ] Подготовка, прояснение задачи и планирвоание (для меня)
            - Идея - првоерять готовность к работе и записывать в статистику. Хороший индикатор срыва и других вещей. 
        [ ] `Неясность задачи`
            - Сказать, что прояснение задачи это тоже учитывается в счет времени, поэтому просто посидите над проектом, посмотрите, почитайте, это все учитывается, это все тоже важно.
            - Можно научить чит-коду, как я вообще часто делаю итерациями. Ваша задача просто посмотреть файл один раз, просто прочитать без всяких там это, посмотреть, может какие-то правки внести, если захочется. Если не захочется, просто прочитайте.
    - [x] Растить интервалы. С ПМ до полноценного. 
    - [x] Быстрая идея, как можно добиться большей продуктивности, это создать fullscreen экран после того, как человек все равно не начинает через пару дней, то есть сказать, все терпение вышло, давай, у тебя больше нет выбора, мне нужно там 3 минуты твоего времени и запускаем какой-нибудь алгоритм по возврату там какие-нибудь, то есть простые вопросы, тебе это вообще важно, да нет, в чем проблема, да нет, то есть такое как бы система алгоритмы возврата.
- [ ] `0.2.` Завершение дня и опрос
    - Завершение интевалов и оценка как сложно было. Важно! Но прям минимальным кол-во кликов. 
- [ ] `0.2.` Статистика 
    > Актуализировать все существующие показатели и добавить новые, которые необходимо добавить на основе существующего функционала.
    - [ ] Статистические блоки
        - [ ] Среднее интервалов в день - рассчитывается некоррекнтно
        - [ ] Стабильность - максимум 1429%. И рассчитывается некорректно. Надо подумать как. 
        - [ ] Качество выходных 
            - Есть ли они вообще
            - Если да, то как проводит время? 
        - [x]`new` Общее время за компом (может быть полезно)
            Можно еще отслеживать общее количество проведенного времени за компьютером и потом смотреть сколько было интервалов. И как-то это учитывает то же, что, мол, дружок, смотри, ты работал столько-то, а за компьютером просидел два раза больше. О каком отдыхе вообще может идти речь и восстановление, пока непонятно в какой это статистический блок добавить, но отслеживать это можно посмотреть и вообще можно как можно больше всего отслеживать.
    - [ ] Статистические точки (цветные) сделать зависимыми от результатов
    - [ ] Сделать сравнение с прошлым периодом (лучше, хуже, на сколько в %)
    - завершение по кол-ву интервалов и (или) времени? Или спршивать как он хочет? 
    - [ ] Чарт в зависимости от выбранного показателя статистики (интервалов по умолчанию - другой цвет)
        - (!!) Так, в окне статистики у нас же там есть чарт и вот он, например, показывает интервалы и можно сделать показ любого вот этого блока статистики, то есть там время начало, там работы, там еще что-то, например, сейчас общее время работы, мы добавили, общее время работы, общее время закомпом мы добавили и вот так вот он поднял и покажет, что там вчера было столько, кто позавчарил столько, ты сегодня столько, то есть каждый показатель можно кликнуть и чарта под него подстроится, это прям мне нравится эта идея, иначе тяжело как бы показывать все элементы, непонятно как все мишанина будет,
    - Если комп закрыт, то качество отдыха лучше. На какой-то процент. Не знаю. 
- [ ] `0.2.` Пользователи, БД, локализация 🇺🇸
- [ ] `0.2.` Актуализировать "НАСТРОЙКИ"
    - а то там много заглушек
    - добавить новые функции

> Делай MVP (помни что досттаочное, а что можно сделать потом)

- [ ] `0.2.` Кайдзен расширенный (без таск-менеджера)
- [ ] `0.2.` Таск-менеджер (простой)
- [ ] `0.2.` Завершение проектов (и возможно планирование)
- [ ] `0.2.` Главный показатель (оформлен в виде fuel-bar)
- [ ] `0.2.` Отпуск
- [ ] `0.2.` Mobile (можно начать с простой функции "уведомление об окончании отдыха)
    - Большая задача, но очень полезная
    - Уведомления о заверщении отдыха приходят на телефон (комп то закрыт)
    - Если получится - отслеживать чем занят во время отдыха, лучше если не использует телефон. Оценивать качество отдыха 
    - Правильный настрой на день при пробуждении. Вовлечение в правильный интервал, вместо тиктока и новостей. Но это только на Android. С iphone тако еневозможно. Подумать как выкрутиться. Можно просто научить - что после пробуждения нужно зайти в апп. 

- [ ] `0.2.` Таск-менеджер (расшироенный с проект-менеджером для kazen)
- [ ] `0.2.` Система диалогов, вовлечения
- [ ] `0.2.` Энергетический профиль пользователя

    **Цель:** Отслеживать пиковые часы продуктивности и адаптировать систему под энергетические циклы

    **Что отслеживать:**
    - Время легкого старта vs сопротивления
    - Длительность успешных сессий в разное время
    - Частота отказов по часам дня
    - Качество работы (субъективная оценка после сессии)
    - Дни недели с высокой/низкой мотивацией

    **Как применять:**
    - Предлагать длинные интервалы в пиковые часы
    - Короткие интервалы в спады энергии
    - Более мягкие сообщения в "тяжелые" часы
    - Адаптивные планки под энергетический профиль

    **Примеры использования:**
    - "Обычно в 10 утра ты на пике - давай 52 мин?"
    - "После обеда обычно тяжело - начнем с 20 мин?"
    - "По понедельникам ты всегда активен - может сразу полную сессию?"

    **Сбор данных:**
    - Ежедневная статистика: начал/не начал, время, длительность
    - Еженедельный анализ паттернов
    - Корреляция с внешними факторами (день недели, погода, сон)

    **Интеграция с системой раннего вовлечения:**
    - Динамическая корректировка матрицы сообщений
    - Персонализированные тайминги предложений
    - Адаптивные планки под текущее энергетическое состояние
- [ ] `0.2.` Стрики
    Психология: Страх потерять достижение сильнее желания получить новое

- [ ] `0.3` [MVP FOR PEOPLE]

`ТЕСТИРОВАТЬ НЕДЕЛЮ, ЗАПИСЫВАЯ ВСЕ ИДЕИ (и не бросаясь решать сразу)`
Пиши идеи, а 1 день в 1-2 недели можно и на этом сфокусировать. 
Но реализуй не все, а самое важное. Иначе легко закопаться. 

## 🧱 My real problems (User Stories)
> Важно! Честно записывай сюда все что не рабоатет в приложении. И думай как решить. И удивишься как приложение реально станет тебе помогать. И не только тебе. 

- 1. Сижу за компом без интервала (мелкая проблема и как часть второй проблемы)
    - [x] Нужен отдых (уже реализовали через напоминание через 52 минуты неактивности)
    - Нужна мотивация начать нормальный проект
- 2. ⚠️ Не могу начать правильный проект и делать долгое время
    - **ГЛАВНЫЙ ФАЙЛ:** `docs/_kaizen-system.md` - Kaizen System: японская философия непрерывного улучшения для борьбы с прокрастинацией
    - Подробная инфа здесь: `docs/project-procrastination-solution.md`
    - **Система устойчивого роста:** `docs/sustainable-growth-system.md` - как не бросить через 1-2 недели и наращивать без выгорания
    - Все действия здесь делятся на 2 категории. 
        1 - статистика (вспомогательно). 
        2 - реальные напоминания и вовлечения (предпочтительно)
    - Надо отслеживать какой проект "правильный", есть ли по нему регулярная активность. И если нет, то предпринимать действия. Какие? 
    - Если совсем плохо и видно что реально не может начать, то либо сокращать интервал, либо если совсем выпал, то начинать растить с 5-15 минут. 
    - Как решить проблему с началом нужного проекта? И регулярным движением по нему?
    - Сделать выбор проекта в настройках. Может на странице проекта выбор проекта для фокуса. Может выделено рамкой. Или (и) иконкой
    - `Начинать как можно раньше`. При открытии окна. И регулярно напоминать если так и не начал. 
    - Сокращать время интервала, если не начал.
    - Сокращать время интервала, если предыдущие дни не работал. Чем больше дней, тем сильнее. 
    - Стрики для целевого проекта. Причем стрики не для проекта, а для факта работы по целевому проекту. Проекты могут меняться. 
    - дополнительно: помогать ставить задачи для входа в поток (посмотреть план)
    - дополнительно 2: работать с барьерами через диалоги. Если лень, и пр. Пока можно простые решения внедрить. Основные проблемы. И короткие решения что делать. Потом можно статьи добавить.
    - доп 3: сказать что тикток и пр сильно влияет. И дать рекламу другого приложения, которое решает эту проблему. И порно
    - в понедельник планка снижается. Для более легкого входа. 
    - Надо посмотреть `work skill` и подумать что внедрить оттуда
    - **Система рабочих навыков:** `docs/work_skills.md` - комплексная методология постепенного наращивания эффективности (20 навыков + система внедрения)
    - **Принципы потока Чиксентмихайи:** `docs/flow_principles_csikszentmihalyi.md` - применение теории потока в uProd
    - **Источники вдохновения:** `docs/books.md` - 17 ключевых книг для развития системы продуктивности (включая GTD и Agile/Scrum для будущего таск-менеджера)
    - Мотивационные короткие фразы:
        - Смотри, ты выпал из потока. Сегодня 15 минут работы над проектом будет лучше чем весь день сидеть и делать непонятно что. Имитировать бурную деятельность. 
        - "Вы работаете уже месяц, это лучще чем 70% пользователей"
    - В статистику - постоянство по целевым проектам. Типа стрики, но иначе. Очень важный показатель. А то непонятно сколько я вообще работаю за год по правильным проектам. Кстати, важно также не размазывать проект долго. Если это на стадии MVP. А то можно год пилить МВП и это тоже неправильно. 
- 3. ⚠️ Не довожу проекты до результата 
    - Для начала надо научиться определять что такое результат. Просто завершать проект? Недостаточно. Надо именно у пользовтаеля спрашивать - вы реально звершили проект и сделали все что нужно или нет? 
    - Причем также надо отслеживать вот эти вот сколько у него проектов без результаты завершены. Вообще есть ли склонность вот так вот метаться между проектами, потому что у меня реально эта проблема есть. И эту склонность нужно учитывать при работе.
    - Возможно, может помочь планирование проекта стадий, то есть определение вместе с ним MVP. И чтобы реально видеть, что вот он эту задачу сделал, то есть мы ему прям учим как вот это все сделать. То есть, что тебе нужны MVP поставить и все такое. И не просто его спрашиваем, сделали ты MVP, а прям видим. Сделал он, не сделал, то есть на какой стадии бросил. То есть это будет более наглядно, чем его спрашивать. Но в первое время можно и спрашивать, пока нет проект менеджера.

## A-grade
- [x] `!` Предлагать отдых даже и без интервала.
    - [x] Если работает уже долгое время, например 50 минут, даже без интервала, то просто предлагать отдых. Типа ты за компом уже 50 минут отдохни. Потому что, например, сейчас я сижу, что-то ковыряюсь, а вот интервал начинать как-то вроде не начал, а вот чем-то занят был, поэтому надо бы как-то сделать отдых. Но прямо интервал считать как-то неправильно. Вот, а отдых нужно сделать, только как вот систему отдыхов выстроить. Точно такую же как и с основным интервалом, типа "иди отдохни". Да, можно пока штаб, а потом дальше посмотрим. А вот вопрос, как считать именно эти 50 минут за компом, то есть у нас же есть каждую минуту проверяется активность, а что если он там на одну минуту отошел, да и активности не было. Может быть, считать, если там, например, 95% из этих всех 52 минут была активность, то высветить сообщение. И, наверное, сообщение это нужно создать отдельно, то есть чтобы оно было, это не будет не то же самое сообщение, типа "Session is completed", а новое сообщение, типа "Ты уже 50 минут за компом, типа "Иди отдохни, сделай перерыв". Ну, как-то вот так, только по уму и на английском языке, и, наверное, из двух строк. И, типа, вторая строка "Want some rest". Ну, типа вот так, а первую я не знаю, как сделать.
    - [x] 08 - Есть подозрение что сработал таймер раньше 52 минут. Что там он учел время до закрытия крышки. И приплюсовал его к времени текущему времени за компом. После сна. Надо секундомер в следуюший раз поставить. Если так, то надо как-то сбрасывать сесиию наверно. Или подумать как решить правильно. 
    - [x] 09-11 - Cделать одну кнопку
    - [x] DRY
    - [x] Еще проблема - нажал later и все. Функционала по сути нету. Надо прям заебать его. Запиннать отдыхать
    - [x] Сделать, чтобы после нажатия на кнопку "Later" в счетчик как бы двигался, то есть появлялся счетчик, что мы вот и работаешь уже 52 минуты, потом 53 и так далее, чтобы как-то ему видно, что он работает. Но непонятно как это все сделать, чтобы когда он там отошел, например, или ноутбук закрыл, как счетчик этот будет работать, то есть он же у нас как вот непонятно здесь. Посмотрим, разберемся. Или вообще, счетчик не нужен, я не знаю, но мне кажется нужен, потому что, как бы, чтобы понимать сколько за компом то сидишь уже, 50 минут или 70 или 200. Хотя для этого можно просто сделать прогрессивную шкалу напоминаний и показывать сколько она уже перерабатывает Ну или таймер, или счетчик, я не знаю, посмотрим, пока не хочу об этом дырыть
    - [x] Если он ушел отдыхать до нажатия "take a break", то собщение не показывается. И Счетчик останавливается. Причем и для формального и для неформальног отдхыха логика должна быть в одном месте. DRY! Но если он вернулся через пару минут (может в туалет ходил), то сообщение покажется на следующем уровне эскалации и счетчик в баре возобновится. 
    - [x]Вообще yelow zone теперь нету окна. 
    - [x] В тесте нет счетчика. Это так и должно быть? Или его и не будет при не тесте?
- [ ] Окна завершения интервала (и формальных и неформальных тоже, так как уже DRY)
    Документация здесь: `/docs/unified-reminder-system.md`
    - [x] Изменить текст кнопки для формального интервала "+1 min" на "I need a couple of minutes"
    - [x] DRY
    - [x] Убрать вторую кнопку
    - [ ] RED zone - тряска стремная
    - [ ] Постоянную пульсацию начиная с RED-ZONE
    - [ ] Стадии экслации побольше сделать.  А то прошли все стадии за 7 минут. 
    - [ ] Последняя эскалация (не делать бесконечное)
        - Либо на весь экран. С невозможностью закрыть. 
        - Либо просто говорится что комп будет заблокирован через (таймер)
    - [ ] Не делать слишком много уровней. 
        Лучше сосредоточиться на качестве. Строже тексты. Если он все равно не идет отдыхать после 3-го, то не пойлдет и после 10-го. 
    - [ ] `!`Если было любое окно, там, Red Zone или желтая зона, или красная, любая, человек просто закрыл крышку, то через пару часов или больше окно потом не сбрасывается. Если он уже отдохнул, ему все равно показывается окно, и таймер красный или какой-то угодно. 
        То есть закрытие крышки и отдых не сбрасывается. Надо сделать, чтобы наверное сколько там. Если больше 17 минуты отсутствовал, то надо сбрасывать все. А то получается я вчера у меня какое-то там было сообщение, я не помню, я закрыл ноутбук, а утром открываю ноутбук, у меня написано Red Zone, и типа непонятно, что делать, вообще, что мне с этим сообщением делать, нажимать Take a Break или мне нужно пару минут. Причем опять же нужно сделать учесть момент, что он может просто закрыть крышку на минуту или ну какое-то небольшое количество и это не должно сбрасывать. То есть сброс должен быть при каком-то достаточно продолжительном закрытии крышки и сна. То есть я не помню, мы где-то уже похожую логику реализовывали, вот попробуй найти это и давай подумаем какое время сделать, чтобы действительно был сброс.
    - [ ] Если он ушел, то может запускать отдых? Если не вернется 17 минут, то отдыха будет засчитан. Еси вернется, то увидит сообщение по системе эскалации. 
    - [ ] Maybe: мотвирующие цитаты: рандомно.
    - [ ] Решить как правильно считать. Он появления окна или от нажатия кнопки "дай мне пару минут"
            - Если А: правильнее показывать экскалацию. И система напоминаний не зависит от нажтия кнопки. Что если он вообще ее не нажмет? 
            - Если Б: Тоже есть проблемы. ЧТо сли он нажмет не сразу, а ближе к концу? То есть если первая эскалация через минуту, а он нажмет через 50 секунд. 
    - [ ] Для неформальных интервалов -  оформить интервал уже пост фактом типа вот ты поработал 50 минут над чем-то, что это интервал или нет, если да то какая категория можно без категории назначить. Но это чуть потом, попозже. Сейчас пока просто сделаем как вот есть.
- [ ] Предлагать работу если нет рабочих интервалов долгое время. Особенно утром?   
    - Можно даже отталкиваться от общего времени проведенного за компом, если, например, уже там 3 минуты он сидит, то можно что-то предлагать, типа доброе утро, 
        там все такое, или там пару минут, если даже первое минуты, доброе утро, там с чего начнем, типа давай там сначала практики, а потом или сразу работа, ну то есть делает для себя в первую очередь, а потом уже будешь подстраивать по других людей кастомизировать, то есть практики, да, окей, можно, кстати, даже настройки делать, интересно, то есть прям флоу делать, то есть как примерно ты себе планируешь день интервалами, то есть первый блок такой-то, там до стальки, то там до восьми, например, у меня практики, потом рабочие интервалы, потом длинный перерыв, то есть ну примерно, да, и он уже примерно будет подстраиваться, то есть хорошая идея, кстати.
    - Типа, что ты не работаешь? Если не работаешь, значит либо лень, либо еще что-то. Либо давай начинать тогда по немножко, либо я не знаю. Особенно если несколько дней, то есть как-то надо потихоньку вовлекать их.
- [x] "Just a couple of minutes" в окне завершения сессии. Вместо двух кнопок. Через пару минут окно откроется заново. 
    Но во вторйо раз уже будет более агрессивный ответ. 
    Эта логика не связана с тем что происходит если не трогать окно вообще. Хотя почему не связано, может быть как раз и будет связано, может быть и будет как раз следовать этой логике, мы же создали константы вот эти вот там через одну минуту у нас текст цвет меняется еще чико потом больше, то есть если он нажал один раз мне типа нужно больше, то у него просто по сути через минуту высвести ценовое сообщение с желтым таймером, то есть если если бы он не скрыл сообщение, то ему просто бы то же самое сообщение с анимацией помегало, а так оно просто новое сообщение вылезет, все по сути нажимает на кнопку на любую, результат примерно будет один и тот же, то есть система сообщений будет примерно та же самая, вот это прям мне нравится
- [ ] Ещё надо как-то отслеживать, наверное, действительно ли он за компом сидит, может он уже ушёл, а мы его тут пугаем. То есть может он не двигает, то есть просто не стал компом включать всё, а он ушёл куда-нибудь, тогда всё считается, вот их нормально зачитывается. 
    - [x] Частично реализовано для окон завершения сессий
    - [ ] Надо сделать для окна обнаружения активности при отдыха
- [x] Выводить текущую версию в контекстном меню в самом последнем пунктам. То есть, например, последняя версия, но сейчас 0.2.4.
- [ ] Архитектурные изменения (позже, надо думать)
    - [ ] Завершение проектов
        - Но как определить настоящее завершение проекта от ложного завершения, которое вроде бы завершили так типа Я тем больше заниматься не буду, но на самом деле не добились, что хотели, как бы бывает как часто?
    - [ ] Как завершить рабочий день? После 6 интервалов? Да, но если досрочно хочет завершить чтобы никаких сообщения не всплывало?
    - [ ] `Досрочное заершение отдыха и начало нового интервала `(+предупреждение, и будет записано в статистику если злоупотребляет)
    - [ ] Другое окно для повторного напоминания о том что пора работать.
        - Другой текст. "Время работать!"
        - Таймер уже не зеленый, а желтый. Тухнущий отдых. 
            - Отдых тоже может тухнуть. Если не начал сессию вовремя.+10 минут это все еще зеленый. Потом желтеет, потом оранжевый. 
        - Лень начинать? Сделать кнопку "Lazy" в окне окончания отдыха. Можно сделать это мя второго напоминания что пора работать. Вместо кнопки "later". 
    - [ ] Актуализировать цветовые индикаторы в статистический блок, чтобы они отражали реальные данные.
    - [ ] Более агрессивные сообщения во всплывающем окне (можно утвердить их список)
        - Чтобы они говорили о важности перерывов
    - Как быть, если у нас есть правило, что если интервал завершен до официального названия, то он не учитывается и то же самое с отдыхом. Если отдых меньше 10 минут, то он не учитывается в статистике, но нам же его учитывать надо, особенно отдых. Чтобы понимать, что вот интервал был сброшен, то есть отдых был сброшен, то есть он начал работу, это же надо все учитывать как-то. Надо об этом подумать. Или как-то надо вот эти вот тестовые отдыхи по-другому учитывать. Или понять, как-то вот эти... Ну у нас вообще прозовершение, как-то вот прозовершение интервалов, там много вопросов. Что делать, если надо досрочно завершить? Я думаю, надо вот это сесть и подумать 
    
- Хорошая идея как завершать день (и получать ответы)- это поставить какую-то планку на день по количеству интервалов и как раз говорить, что вот сегодня мол три интервала у тебя, в зависимости от его продуктивности и после этого как раз оценивать, ну если он нажал все типа хватит на сегодня после этого оценивать как вообще было легко, сложно или как


## B-grade (later)
- [ ] Пользователи. Авторизация и БД. 
- [ ] Для окна обнаружения активности во время отдыха тоже можно сделать тряску (если не скрыто). 
    - Или вообще заканчивать отдых, если уже там, то есть 3 минуты он провел активности во время отдыха, отдых просто завершается, автоматически, ну как бы причем, непонятно, что будет дальше, то есть его в интервал включить или куда, но не знаю пока непонятно. Надо думать. Пока просто идея. 
- [ ] Если буду игнорировать эти напоминания. 
    То можно будет что-то с этим делать. Например на весь экран окно, с невозможностью закрыть и с кратким напоминанием к чему ведёт переработка (потеря уверенности, снижение продуктивности, тревожность, отсутствие наслаждения). Сделай вдох и или погуляй. Никуда от тебя работа не денется.
- [ ] Как начинать работу с нуля
    два элемента - работа - первое дело дня. И `практика Павла психитра` - делать работу или ничего 
- [ ] 📊 Улучшение статистических блоков
    - На недельном 
    - На дневном
    - Без категории (любые идеи по статистическим блокам) - чем больше данных собираем, тем легче будет анализировать
        - [x] Среднее время отдыха (поможет получше понять жффеткивность)
- [x] Время переработки показывать + к времени сессии. Это будет более наглядно, сколько в итоге интервал длится. Так же, как это сделано в отдыхе.
- [ ] Адаптивные интервалы
    - в первую очередь после долгого отдыха. Может меньше. 
        - Но непонятно насколько. И нужно ли. Может он и без этого нормально рабоатет. 
        - Может только для тех, кому тяжело. А как понять что ему тяжело? Это кстати важная функция, которая является основой адаптивности. Можно сделать автоматически на оснвое статистики последних интервалов (паттерны потока и прокрастинации легко узнать). Либо спрашивать. 

- [x] Прибраться в шапке. 
    - [x] Сделать выбор периода в одну строку. Одной высотой. И справ добавить икноку профиля. Стрики и настройки
    - [x] Выровнять отступы
- [ ] Статистика только по проекту (от начала до конца, без привязки ко времени) 
    - Частично реализовано на странице проектов, но нужна более подробная статистика в формате статистики со всеми статистическими плашками и чартами.
- [ ] галочка "Показывать завершенные проекты". Или архивные? И вообще сделать выбор при редактировании проекта "завершен или нет"
- [ ] Статус проекта "завершено". И в интерфейс галочку "скрыть завершенные".
- ВО ВРЕМЯ СЕССИИ:
    - [x] Иконка проекта уходит вправо от таймера, а не слева
- 
- Главный показатель (оформлен в виде fuel-bar)
    - Показывает баллов за день. Зависит от множества параметров. Работал ли над главным проектом. Плюс неплохо было бы чтобы была цепочка постоянства, которая сбрасывалась если что-то не то. 
    - Непонятно как его формировать. За день? За неделю или за любой период? Ведь если не работал над главным проектом, то будет 0. А если потом начал? Вопросов много. Надо выделить время и думать. 
- Контекстное меню красивое, со стриками и всем необходимым. 

## C-grade
- [x] В настройках "автоматическое применение рекомендацмй. 
    - Ну то есть, например, поняли мы, что слишком ему много будет 52 минуты интервала у него, он с этим-то не справляется и ему автоматически будет понижаться интервал. Если галочка этого набирает, то тогда не будет, тогда будет как бы всплывающее сообщение. Типа, вот мы хотим понизить интервал, согласны, не согласны и так далее.
- [x] Лонг брейк можно привязать ко времени. Например ближе к 12 или 1 пм
- [x] Можно сделать так, что в настройках выбрать типа основной проект для фокусировки и по нему система будет как бы вовлекать в работу, 
    да, отслеживать, типа есть по нему работа, нету и ну каждый день, да, то есть работает или нет. Пока в настройках тупо его можно дать, а дальше он как бы будет через диалоговое окно, наверное, спрашиваться.
- Интеграция с мобильным приложением 📲
- Система отпусков
    - Учесть все варианты - если рабоатет на себя и гибкие отпуска, то можно дать системе право формировать отпуска. 
- 

## Непонятно (но проблема есть)
- [ ] `Что если надо прекратить на половине интервала? Кушать например.` 
    - Я еще не знаю, как вообще быть с этими моментами, когда вот крышка закрывается и нужно сбросить интервал. И вообще, вот как быть, когда вот, например, я работаю, вот 30 минут, да, и мне, например, там жена позвала обедать, я вроде бы отказаться не могу, она уже все разогрела. И как мне сбрасывать интервал, на паузу ставить его, тоже неправильно. Завершать, ну он будет неполноценный, я не знаю, как это. Ну и сбрасывать тоже неправильно. Поэтому вопрос пока открытый
    - Если оставлять таймер такой вот как бы неполноценный то есть на половинче то или сколько там 30 минут 20 минут то будет ли он вообще учитываться потому что мы же как бы учитываем только таймеры полноценные то есть сколько там если 52 минуты то 52 остальные мы просто в статистику не берем как погрешности и считаем их как как тестовы или как какие-то такие непонятные
    - Сбрасывать, наверное, неправильно, потому что, ну вроде как он поработал с другой стороны, учитывать тоже вроде бы это неправильно, потому что это неполноценный интервал. Можно учитывать частично во время работы, наверное, но интервал как полноценный, наверное, мы не будем учитывать. Короче, я не знаю, надо подумать.
- [ ] Идет ли таймер если закрыть крышку ноута? Останавливается. Надо чтобы сбрасывался? Продолжение предыдущей проблемы
-  Но как определить настоящее `завершение проекта` от ложного завершения, которое вроде бы завершили так типа Я тем больше заниматься не буду, но на самом деле не добились, что хотели, как бы бывает как часто?








# OLD (messy)

## 0.1 - pomodoro
- [x] base timer
- [x] Bug: после нажатия +1 мин не появилось окно с напоминанием через 1 минуту. Нужно починить
- [x] Если переработка, то текст таймера меняется. Слегка желтый после 1-й минуты. Орандевый после 3-х минут. И красный после 5 минут. И темно бардовый после 10 минут. После 15 минут появляется смайлик с закипающим мозгом 😤
- Анимация кнопок "заверщить" и продлить"
- [x] Base stats (сколько интевалов полноценных (именно 52 минуты прошло, а не заверешнных вручную ) за сегодня, вчера, неделю, месяц, год, всего за все время)
    - [x] Страница статистики. Пока что простая. Тупо сколько интервалов за день, неделю, месяц, год, всего за все время. Графики не нужны. Тоже самое для переработки. 
    - [x] Как анализировать и давать рекомендации? Спроси ЛЛМ. Анализ постоянства, поздно начинает, перерабатыает, риск выгорания. 
- [ ] Графические элементы. Непонятно пока как. Либо просто в виде интервалов, с переработками. Либо статистику по дням. Например если это неделя, то 7 столбиков. И в каждом. Тут надо сначала подумать для недель как показать. Не надо сразу для всех периодов думать. Тут много вариаций даже для недели. Как показывать? Просто интервалы? Нет. 
    - [x]разные цвета в зависимости от кол-ва интервалов. До 1-2 синий. От 3 до 5 зеленый.  6 желтый.  7 оранжевый. От 8 и выше красный. 
    - Легенда по дням (ось X) и кол-ву интервалов Y



- [ ] Проекты
    - Сделать проект "без категории". Третий. Когда просто открываем комп и непонятно что делаем. 
- [ ] Контекстное меню не стандартное, а кастомизированное. Чтобы дизайн можно было настраивать. И Там сделать разные версии проектов. С возможностью быстрой смены проекта.  И СПИСОК ЗАДАЧ GG НА ДЕНЬ - ЧЕКЛИСТЫ ПО ПЛАНКАМ. Кол-во интервалов сегодня. СТрики может быть. Скор. (мини-дашборд короче). Да, многих элементов нету, но надо создать болванку, на которую потом буду насаживать. 













- Для каждого статистического блока можно сделать четыре вида градации, красненькую, оранжевую, желтую, зеленую. То есть в виде батарейки или как у меня вью мема есть для индикатор новых слов.
- Вообще подумать как сделать для дней, как для неделей, месяцев и лет. Разные или одинаоквые элементы будут? 
    - [ ] Статистика для дней. Там иначе все будет. Подумать как. Я буду тестить каждый день на реальной работе. И смотреть как он спавится. 
        - Если все хорошо, то говорить что все хорошо. Нет рекомендаций, продолжай в том же духе
    - чарты (если месячные, то можно сделать выборку по дням, а можно по неделям). Годовые тоже самое. Можно по месяцам, можно по неделям. 
- Подумать какие показатели считать в статистике. Более детально. 
    - Может сделать Score-постоянства справа обособленным. Как стрелка бензина. Или как линию в пространстве, чтобы удерживать ее. 

- 🌡️ SCORE
    Возможно будет состоять из нескольких показателей
    - 1. Постоянство (сколько дней за неделю работал) 4 из 5? Значит 0.8 (80%)
    - 2. Стабильность итервалов (нет резких скачков)
    - 2.5. Нормальное кол-во интервалов в день. 4-5. Чем меньше, тем хуже показатель. Чембольше, тем тоже хуже.  
    - 3. Нет переработок 
    И отследивать его во времени. По неделям. И выводить в дашборде как кривую. Кстати, можно его также оценивать и во времени.
- Проблема - нет выходных. В анализ недели и месяца. В настройки - работать в выходные? По крайней мере в субботу. Да, короткий день. Но лучше не надо. 
- Критические рекомендации. И допустимые. Например перербаотка +5-10 минут к интервалу это ок. 10-20 это уже медиум. Более это уже хай-риск. Потом По кол-ву интервалов в день. 6 это уже низкий риск. Более это уже критично. И так каждый элемент разобрать. Время начала. 8-9 это лоу. 9-10 это медиум. После 10 это уже хай риск. Возможно кстати давать в самих плашках оценку риска. И рекомендации. А то таблицы прям не очень. 
- Проекты. Описать что хочу и Спросить как
- Не даст начать интервал, если не прошло время отдыха. 17 минут для коротких и 60-90 для длинного. 
    - + сообщение, что не прошло время отдыха
    - Кнопка "начать все равно, осознаю риски"
- Звуки 
    - заверщения интервала
    - продлений (1, 2, 3) - агрессивнее
    - возможность выбора звука в настрйоках
- Конфети после звершения интервала
- Анимации окна
- Время переработки (дублировать таймер) в окно 
- После завершения отдыха предложит начать интервал, в таком же всплывающем окне, но другом.
- окно всплывающее более прозрачным. И с глассморфизмом
- Если окошко открыто в оверворке, то можно сделать его анимацию короткую, что мол иди отдыхай
- Кнопка "начать отдых" тоже нужна. С таймером. Не у всех часы есть. Но надо сделать интеграцию с телефоном. Потому что если ноут закрыт то что? Но это потом. Пока для себя делаю. 
- БД и авторизация


Так, некоторые элементы статистики можно скрыть, такие как например время начала, среднее время, там окончание работ. Они как бы, ну или там сделать их поменьше, просто они такие не основные, но они используются для рекомендаций. Тоже, потому что если там человек заканчивает работу там ночью, то ну как бы хреново ему будет, потом быстро. Вот и 

Сами рекомендации тоже давать можно как бы кратко, то есть сделать просто их список, приоритизировать, но это в документации, а в самом приложении давать кратко и там давать какую-то типа там, смотреть, читать подробнее или там, ну не знаю, как это сделать красиво. Там уже подробнее рассказывать, для чего это нужно, почему это важно и уже прям совсем, если они хотят подробнее, то уже там переводить на какую-то там страничку в интернете, где рассказывать, почему это вообще важно, прям там уже, ну обычная статья, типа там мини-превычки, все такое, то есть получается краткая информация, просто краткий, сначала краткая, прям краткая, краткая, потом чуть поподробнее, если надо и уже прям совсем подробнее тоже на сайте.



## 0.2 - авторизация для сохранения статистики

## 0.3 - score постоянства
- Как считать? Не знаю. Расскажи что думаешь для ЛЛМ и спроси решение


===
- Короткий интервал (25 минут) - прям в контекстном меню, под основным. И который кстати будет перерастать в обычный
- После 12 создаются короткие

## Адаптивный интервал
- Для тех, кто не может начать - 1-5 минут. Или вообще прям целая программа влючения в поток. Нажимает SOS и вводит в поток. Хотя можно и без нажатия SOS это делать. Видит что давно не работал. Значит выпал очевидно. Значит надо возвращать. Начать с малого и наращивать, собирая обратную связь. 

    - То есть программа видит, она там, ты выпал, да, она предлагает укороченный интервал. Если ты и его не начинал, то она предлагает еще более укороченный, там, 5 минут поработать, там, и каждый раз она предлагает, давай там, хотя бы там, задача как-то там, посмотрим, что у нас есть, на чем мы застряли, или там, если она там, чувствую, что застрял, вот, давай попробуем алгоритм преодоления барьеров и так далее, то есть разные варианты пробовать. 
    - Кстати, можно вгнедрить методику Павла психиатора - делай дело или ничего. С мини-обучением. 

Что-то неплохо было бы вот так вот начать работать, да, то есть человек сидит, не может собраться и ему так баться, сообщения всплывать. Ну что-то там я вижу ты уже два дня не можешь начать, давайте по работе. И он говорит, ну и там адаптируется интервал уже естественно, там хотя бы 15 минут ему предлагается. Он говорит, нет, типа у меня нету идей там или что-нибудь такое. И ему там вариант предлагается, там сразу же, давай там поищем идей, там и все такое. Или как-то там я застрял, то есть в зависимости от ему подставляются варианты. И то есть увлекает его в процесс, вот это прям очень важно. То есть мы из любого дерьма его вот так скажем, из переработок, из барьеров, из нет идей и так далее и тому подобное.

- Можно вводить мини-обучения. 
    - Как отдыхать (без тиктока, экранов)

## Как быть с опросами? 
Непонятно куда из внедрять. Не зочется отвечать на вопросы. Даже мне. А юзерам тем более
Варианты 
    - внедрить вопросы в процессы. И вовлекать.
    - просто вечером задавать один вопрос (устал ли), потом другой 

## Стрики 
Стрики, наверное, будут не в статистике, а просто где-то наверху, как в дуалингу, в принципе, сделаны. И также с темы уведомлений будет каждый вечер, что типа, "Эй, дружок, там осталось только то времени, давай" типа один оторвал хотя бы, если он не сделал, конечно, если сделал, то все хорошо. И также его учим, что типа, стрики это вообще как бы входит в топ-3, чуваков с высокой продуктивностью, то есть они выполняют стри, типа, "это мы вы выполняете". Типа, если вы будете там хотя бы минималочку делать, там каждый день 20 минут, то вы через год все они узнаете. Ну и там можно в кратской мини-привычке книгу пересказать там на паре экранах, а даже на одну.

-------------

# v1 - Task-manager
Попробовать месяцок, может получится что-то интересное. Для лчиного использования.

**ВАЖНО: Сразу делать по AGILE и лучшим мировым практикам!**

## 🚀 Agile/Scrum подход для таск-менеджера:
- **Недельные спринты** вместо хаотичного планирования
- **Канбан-доска:** To Do → In Progress → Done
- **Story Points** интегрированные с интервалами (1 SP = 1 интервал)
- **Ретроспективы** каждую пятницу для улучшения процесса
- **WIP limits** - не более 3 задач одновременно в работе
- **Burndown charts** для визуализации прогресса
- **MVP подход** к проектам из Lean Startup
- **GTD система:** Inbox → Clarify → Organize → Review → Engage
- **Правило 2 минут** для мелких задач
- **Контексты** для группировки задач (@компьютер, @звонки, @дом)

Подробно: `docs/books.md` - разделы GTD и Agile/Scrum

## Задачи реализации:
- Сделать анализ всех основных конкурентов (что нравится, что не нравится)
- Но надо будет решить главную проблему - как показывать все правильно без путаницы с тасками и подтасками.
    - Прям сфокусироваться на этой проблеме, нырнуть в нее. Описать что бесит. И посоветоваться как решить.
    - канбан с Agile-принципами

# Геймификация 
- Стрики (в первую очередь) 
Очки за качественный отдых
Достижения за стабильность
Штрафы за переработки

# IDEAS
- Сделать интеграцию с другими моими приложениями. Чтобы он мог во время интервалов например спортом начать заниматься. Или счастье практиковать. И ремайндерсы делать. 

🎯 Умные рекомендации
- "Сегодня лучше начать с легких задач"
- "Вы обычно продуктивны в 10-12"
- "Пора сделать перерыв подольше"

📈 Анализ паттернов
- Выявление оптимальных времен работы
- Предсказание спадов энергии
- Персональные ритмы

🚨 Раннее предупреждение
- "Риск выгорания через 3 дня"
- "Снижение качества работы"
- "Нужен день отдыха"

Еще неплохая идея сделать лидер борт по наш бытам главный показатель, главный скор. И вот по нему сделать лидер борт и возможности по социальной активности смотреть его лент, у чем он там пишет, и это будет мотивировать людей, как бы... То есть можно на этом тоже зарабатывать как-то. То есть он становится лидером мнений, то есть как-то продавать свои курсы, потом дополнительно по эффективности, то есть советы давать. То есть как-то вот так. И мы тоже будем понимать, что вот у нас там лент и борт, и так он работает, то есть его статистику можно посмотреть и так далее.