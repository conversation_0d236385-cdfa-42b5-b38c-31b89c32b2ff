Так, ну короче, что сегодня мы работаем, продолжаем работать с над системой внедрения раннего пропуждения адаптивных интервалов инструкции в соответствующих местах лежат Можешь изучить для лучшего понимания @/Users/<USER>/EVO/uProd/docs/early-engagement-task.md 

Сегодня мы будем работать над кнопками. Над матрицей кнопок. Смотри 166-176 строки в файле @/Users/<USER>/EVO/uProd/todo.md 

Для лучшего понимания по кнопкам советую изучить `/docs/session-growing-system.md` и системы полных планок `/docs/full-session-system.md`

У нас же же есть некая матрица сообщений для раннего пробуждения. Вертикаль и горизонталь. Где рассчитывается сколько минут должен работать. Вот также понятно и предсказуемо должно быть с кнопками. 

А то сейчас там просто тупо кнопки в виде текста. А должно быть как модули. Например: 
- Кнопка текущей планки
- Кнопка полной сессии (52 мин) `/docs/full-session-system.md`
- Кнопка план-минимум (3 мин)
- и т.д.

Единственное, что пока понятно:
- Первая кнопка (основная) будет размером текущей планки. Но не всегда, там есть исключения. В предложениях по горизонтали (в течении дня) вместо нее будут дескалирующие планки (например -50% для второго сообщения). Например если текущая планка сегодня 30 минут, то для первого сообщения дня будет 30 мин, а для второго 15. Потом третье сообщение фиксировано 15 мин. И четвертое фиксировано 3 мин.
- В матрице по горизнтали для 3 и 4 уровней первая кнопка уже понятна. 15 минут и 3 мин в соответсвии с 3 и 4 уровнем горизонтали
- Дополнительная кнопка - полная сессия. 

Матрица немного усложняется тем, что будет не одна кнопка, а несколько. 

Все это надо внедрить так, чтобы в отладочном окне я мог выбрать окно по вертикали, горизонтали и увидеть кнопки. Но не просто текстом захардкожены, а чтобы подставилась нужная, заранее определенная кнопка. Типа как компонент. 

В отладочном окне сейчас работает только расчет текущей планки (расчетная планка). Тексты и кнопки там просто захаркожены, что неправильно. 

Давай рассмотрим примеры того как должны формироваться кнопки. 

1. Допустим он работал вчера (0 уровень по вертикали). ИЗначальный интервал +30 мин. 

📍 Позиция: 0 дней (вчера работал) × 1-е сообщение (сразу при пробуждении)

⏱️ Изначальная планка: 30 мин
🧮 Расчет: 30 +14% × 1.0 (100%) = 35 мин
✅ Рассчитанная планка: 35 мин

📝 Заголовок: "Продолжаем работу с uMemo?"
📄 Текст: "Вчера был продуктивный день. Начнем с 35 минут?"
🔘 Кнопки: "[Начать работу (35 мин)]" | "[Начать полный интервал (52 мин)]" | "Позже"

2. Второй пример. Допустим он 7 и более не работал над приоритетным проектом
 

При этом надо как-то учесть момент что при горизонтали для второго и последующих сообщений нужно второй кнопкой показывать:
- Либо полную расчетную планку. Мы же вычли 50%. Например текущая планка была 30 мин. Второе сообщение будет 15 мин. Тогда вторая кнопка будет 30 мин. 
- Либо полную сессию (52 мин). Я не знаю что лучше. Может это нужно показывать третьей кнопкой, а может это уже перебор. 

---

Давй сначала обсудим здесь концепцию, найдем правильное решение. И потом только будем внедрять. Изучи существующий проект и все что нужно через codebase-retrieval.

Используй MCP-сервер Sequential Thinking. Если это необходимо. 


