# 🌅 ЗАДАЧА: СИСТЕМА РАННЕГО ВОВЛЕЧЕНИЯ

> Решить вопрос Как вообще начинать вовлечение по приоритетному проекту? То есть первое или вообще после установки приложения, какой должен, какой будет первый интервал? Хотя может и калибровку какую-то сделать, то есть прям, ну можно пару вопросов задать, конечно, а можно сказать так, ну давай начинать и посмотреть насколько справишься, ну и как бы постоянно спрашивать, ты как, окей, не окей, там типа, ну то есть как-то так.

## 🎯 ГЛАВНАЯ ЦЕЛЬ
Помочь пользователю **регулярно начинать работу** над приоритетным проектом утром с помощью **двойной системы эскалации**, принципа **"план минимум"** и **адаптивных интервалов**.

## 🔄 КЛЮЧЕВЫЕ ПРИНЦИПЫ

### **Принцип "Интервал начинается сразу"**
Как только пользователь отвечает на любой вопрос → интервал уже начался, продолжаем направлять его в работу.

### **Двойная система эскалации:**
1. **По дням без работы** (долгосрочная) - влияет на тип первого сообщения
2. **По времени в течение дня** (краткосрочная) - повторные предложения с дескалацией интервалов

### **Дескалация интервалов в течение дня:**
- Первое предложение: полный интервал (например, 52 минуты)
- Второе предложение: сокращенный интервал (например, 25 минут)  
- Третье предложение: план минимум (2-3 минуты)
- Финальное: полноэкранное "сейчас не начнешь - сегодня не начнешь"

### **Адаптивные интервалы по дням:**
- Работал вчера → стандартный интервал (52 мин)
- Не работал 1 день → сокращенный интервал (30 мин)
- Не работал 2+ дня → короткий интервал (15 мин)
- План минимум всегда доступен (2-3 мин)

---

## 📋 ЭТАПЫ РЕАЛИЗАЦИИ

### **ЭТАП 1: Детектор пробуждения** 🌅
**Что делаем:** Система понимает, когда пользователь проснулся и включил компьютер

**Результат:** 
- Предложение начать работу в самый продуктивный момент
- Стартовые сообщения сразу после пробуждения

**Технические детали:**
- Отслеживание времени последней активности
- Определение "первого запуска дня"
- Интеграция с системой уведомлений

---

### **ЭТАП 2: Умная аналитика дней без работы** 📊
**Что делаем:** Отслеживаем статистику работы над приоритетным проектом

**Результат:**
- Система "помнит" историю работы
- Основа для адаптивных сообщений и интервалов
- Статистика для эскалации

**Категории (обновленная градация):**
- **Уровень 0** (работал вчера) → позитивный настрой, стандартные интервалы
- **Уровень 1** (вчера не работал) → мягкое напоминание, понимающий тон
- **Уровень 2-3** (2-3 дня не работал) → усиленная мотивация, настойчивый тон
- **Уровень 4-6** (4-6 дней не работал) → серьезная эскалация, прямой тон
- **Уровень 7+** (неделя+ не работал) → критическая эскалация, полный экран

**🔮 Будущие улучшения:**
- Анализ постоянства за последнюю неделю/месяц
- Учет долгосрочных паттернов работы
- Персонализация сообщений под показатель постоянства
- Подстройка тона под общую статистику пользователя

---

### **ЭТАП 3: Система умных сообщений с двойной эскалацией** 💬🚨

#### **3.1 Концепция и принципы** (высокий уровень)

**Двойная система эскалации:**
1. **ВЕРТИКАЛЬ - По дням без работы** → определяет РАЗМЕР текущей планки на сегодня
2. **ГОРИЗОНТАЛЬ - По времени в течение дня** → определяет ИНТЕРВАЛЫ от этой планки

**Принцип "Интервал начинается сразу":**
Как только пользователь отвечает на любой вопрос → интервал уже начался, продолжаем направлять его в работу.

#### **3.2 Матрица построения сообщений** (средний уровень)

### **Принципы логики построения сообщений:**

**Двумерная матрица (5 x 4 = 20 вариантов сообщений):**

### **📅 ВЕРТИКАЛЬ - Дни без работы**
**Функция:** Определяет РАЗМЕР текущей планки пользователя на сегодня

> **📋 Подробная документация:** См. файл `docs/adaptive-user-bar-system.md`

**Принцип работы:**
Система анализирует сколько дней пользователь не работал и **пересчитывает его текущую планку** по формуле:

**Формула пересчета планки:**
- **Уровень 0** (работал вчера): планка растет по [градационной системе](gradual-growth-system.md) (+10% до +60%)
- **Уровень 1** (1 день пропуск): планка × 0.77 (-23%)
- **Уровень 2-3** (2-3 дня): планка × 0.58 (-42%)
- **Уровень 4-6** (4-6 дней): планка × 0.29 (-71%)
- **Уровень 7+** (неделя+): план-минимум (3 мин) фиксированный минимальный уровень планки.

**Пример:** Если вчера планка была 40 мин, а пользователь пропустил 1 день:
→ Сегодня планка = 40 × 0.77 = 31 мин

### **⏰ ГОРИЗОНТАЛЬ - Эскалация в течение дня**
**Функция:** Определяет ИНТЕРВАЛЫ от текущей планки **ТОЛЬКО до первого успешного интервала**

**Принцип работы:**
Берем текущую планку (рассчитанную по ВЕРТИКАЛИ) и от неё делаем до 4 предложений:

- **Предложение 1**: 100% текущей планки
- **Предложение 2**: 50% текущей планки
- **Предложение 3**: Фиксированный минимум (15 мин)
- **Предложение 4**: План-минимум (1-3 мин) + полный экран

**Тайминг показа сообщений в течение дня:**
- **Сообщение 1**: Сразу при открытии компа (детектор пробуждения)
- **Сообщение 2**: Через 20 минут после первой активности
- **Сообщение 3**: Через 1 час после первой активности
- **Сообщение 4**: Через 2 часа после первой активности ИЛИ в 14:00 (что раньше)

**🔥 КРИТИЧЕСКИ ВАЖНО - Обнуление после успеха:**
Как только пользователь **ЗАВЕРШИЛ** любой интервал (не просто начал, а довел до конца):
→ Вся система дескалации на день **ОБНУЛЯЕТСЯ**
→ Планка **ФИКСИРУЕТСЯ** на весь оставшийся день
→ Больше никаких предложений в этот день. Только стандартные формальные и неформальные сообщения о завершениии интервалов и отдыхов. 

**Пример:** Если текущая планка = 30 мин:
- 1-е предложение: 30 мин (100%) → Пользователь согласился и завершил
- **РЕЗУЛЬТАТ:** Планка 30 мин зафиксирована на весь день, дескалация отключена. Также 30 мин будет использована на следующий день при первом сообщении. Потому что сегодня он поработал. 

### **🍽️ Послеобеденная адаптация планок `НЕ ВНЕДРЕНО, ПОЗЖЕ`**

**Проблема:** После обеда энергия и концентрация снижаются, полные планки могут быть слишком тяжелыми.

**Решение (будущая реализация):**
1. **Автоматическое снижение** планок после 14:00 (например, -20-30%)
2. **Предложение "добить"** - после сниженного интервала спросить: "Остались силы добить до полной планки?"
3. **Сбор обратной связи** - "Какой интервал лучше: 40 мин или 52 мин?"
4. **Адаптация на основе ответов** пользователя

**Критика решения**
- Не всем нужны послеебеденные адаптации, не у всех спад

Поэтому (финальное решение): 
- (!) Сокращенный интервал сделать опциональным
- Предлашать добить сниженный интервал

Есть еще проблема. После выходных тоже юарьеры могут быть и лучше снизить планку. 
 

**Пример работы:**
```
15:00 - Обычная планка 52 мин → Предлагаем 40 мин
Пользователь завершил 40 мин → "Добить еще 12 мин?"
Если ДА → В конце спросить предпочтения для будущего
```

В сообщения можно привязать к времени. например "Уже час после включения компа, ты наверняка уже увлекся чем-то, что кажется более важным. Но давай порабоатем над приоритетным проектом? И потом продолжишь с чувсвтом выполненного долга, а не гнета"


### **🔄 Как работает матрица: пошаговый алгоритм**

**ПОЛНЫЙ ПРИМЕР РАБОТЫ СИСТЕМЫ:**

```
СИТУАЦИЯ: Пользователь не работал 2 дня, сейчас утро, вчера планка была 50 мин

ШАГ 1 - ВЕРТИКАЛЬ (определяем новую планку):
2 дня без работы = Уровень 2-3 → применяем коэффициент -42%
Новая планка = 50 × 0.58 = 29 мин

ШАГ 2 - ГОРИЗОНТАЛЬ (рассчитываем предложения от планки 29 мин):
• 1-е предложение: 29 мин (100% планки) → Пользователь отказался
• 2-е предложение: 15 мин (50% планки) → Пользователь согласился и ЗАВЕРШИЛ

ШАГ 3 - ОБНУЛЕНИЕ:
Интервал завершен → Планка 29 мин ЗАФИКСИРОВАНА на весь день
Дескалация ОТКЛЮЧЕНА → Больше никаких сниженных предложений

ШАГ 4 - РЕЗУЛЬТАТ:
Остаток дня: все предложения по 29 мин (без дескалации)
Завтра: планка вырастет, т.к. пользователь работал
```

**Алгоритм выбора сообщения:**
1. **ПРОВЕРКА:** Работал ли пользователь сегодня? Если ДА → планка зафиксирована, дескалация отключена
2. **ВЕРТИКАЛЬ:** Определяем уровень по дням без работы → рассчитываем новую планку
3. **ГОРИЗОНТАЛЬ:** Определяем номер предложения в дне → рассчитываем интервал от планки
4. **ТОН:** Выбираем тон сообщения на основе уровня дней без работы
5. **СООБЩЕНИЕ:** Формируем итоговое сообщение с учетом всех факторов
6. **ПОСЛЕ ЗАВЕРШЕНИЯ:** Если интервал завершен → фиксируем планку, отключаем дескалацию

---

#### **3.3 Примеры сообщений по матрице** (детальный уровень):

**УРОВЕНЬ 0 (работал вчера) - Первое предложение:**
```
"Доброе утро! Готовы продолжить работу над [Проект]?
Вчера отлично поработали, продолжаем в том же духе!

Предлагаю стандартный интервал 52 минуты.

[Начать 52 мин] [Другой интервал] [Не сейчас]"
```

**УРОВЕНЬ 1 (вчера не работал) - Первое предложение:**
```
"Привет! Понимаю, вчера был сложный день.
Сегодня важно вернуться к проекту [focused_project] , пока это не стало тяжело .

Предлагаю стандартный интервал 40 минуты.

[Начать 40 мин] [Начать нормальный интервал 52 мин] [Не сейчас]"
```

**УРОВЕНЬ 2-3 (2-3 дня не работал) - Первое предложение:**
```
"Время вернуться к [Проект]!
Несколько дней перерыва - это не страшно, главное не затягивать.

Предлагаю сокращенный интервал 30 минут для мягкого возвращения.

[Начать 30 мин] [План минимум 3 мин] [Не сейчас]"
```

**УРОВЕНЬ 4-6 (4-6 дней не работал) - Первое предложение:**
```
Уже почти неделя без прогресса - пора что-то менять.

Предлагаю короткий интервал 15 минут для возвращения в ритм.

[Начать 15 мин] [План минимум 3 мин] [Не сейчас]"
```

**УРОВЕНЬ 7+ (неделя+ не работал) - Первое предложение (ПОЛНЫЙ ЭКРАН):**
```
"🚨 КРИТИЧЕСКАЯ СИТУАЦИЯ!

Неделя без работы над [Проект]
Этот проект все еще важен для вас?

Если да - начинаем прямо сейчас с минимума. Посмотреть план, найти барьеры. 
Если нет - честно закрываем. 

[Да, начать 3 мин] [Закрыть проект] [Еще день подумать]"
```

#### **3.4 Примеры эскалации в течение дня** (детальный уровень):

**УРОВЕНЬ 0-1 - Второе предложение (дескалация интервала):**
```
"Может, попробуем сокращенный интервал?
Лучше 25 минут сейчас, чем полная сессия "потом".

[Начать 25 мин] [План минимум 3 мин] [Не сегодня]"
```

**УРОВЕНЬ 2-3 - Второе предложение (дескалация интервала):**
```
"Понимаю, 30 минут может показаться много.
Давайте 15 минут - это совсем немного!

[Начать 15 мин] [План минимум 3 мин] [Не сегодня]"
```

**ЛЮБОЙ УРОВЕНЬ - Третье предложение (план минимум):**
```
"Хорошо, давайте план минимум - всего 3 минуты.
Streak не прервется, регулярность важнее интенсивности.
Это время что напомнить что нужно делать (полагаю вы забыли). 

[План минимум 3 мин] [Не сегодня]"
```

**ЛЮБОЙ УРОВЕНЬ - Финальное предложение (ПОЛНЫЙ ЭКРАН):**
```
"⏰ ПОСЛЕДНИЙ ШАНС СЕГОДНЯ

Если не начнешь СЕЙЧАС, то не начнешь СЕГОДНЯ.

Всего 2 минуты - и день не пропадет зря.

[2 минуты] [Честно: не хочу сегодня]"
```

#### **3.5 Проверка готовности** (технические детали):

**Встроенная проверка в сообщения:**

**Вариант с проверкой ясности:**
```
"Готовы поработать над [Проект]?

Но сначала: ВАМ ЯСНО ЧТО ДЕЛАТЬ?

[Да, ясно - начать] [Не очень - проясню] [Совсем не ясно]"
```

**Ответы:**
- **"Да, ясно"** → Начинаем предложенный интервал
- **"Не очень"** → "Проясни задачи - это тоже считается интервалом!" → Начинаем интервал планирования
- **"Совсем не ясно"** → "10 минут на прояснение - и это уже работа!" → Интервал прояснения

---

### **ЭТАП 4: Система "План минимум"** ⭐
**Что делаем:** Внедряем принцип минимальной планки каждый день

**Ключевые принципы:**
- **"Лучше 2 минуты, чем ничего"**
- **"Одно отжимание лучше нуля"** 
- **Сохранение streak'а и регулярности**
- **Честный выбор: план минимум или "не хочу сегодня"**

**Варианты плана минимум:**
- 2-3 минуты работы
- Прочитать одну страницу
- Написать одно предложение  
- Открыть файл проекта и посмотреть

**Рост плана минимум:**
- Начинаем с 2 минут
- Постепенно растим до 5 минут (отдельная задача)

---

### **ЭТАП 5: Адаптивные интервалы** ⚙️
**Что делаем:** Автоматически подстраиваем длительность интервалов под историю работы

**Логика адаптации (обновленная с концепцией "текущая планка"):**

### 🎯 Расчет текущей планки пользователя:
- **Уровень 0** (работал вчера) → планка остается (52 мин)
- **Уровень 1** (вчера не работал) → планка слегка снижается (45 мин)
- **Уровень 2-3** (2-3 дня) → планка снижается (-30%, ~35 мин)
- **Уровень 4-6** (4-6 дней) → планка сильно снижается (-50%, ~25 мин)
- **Уровень 7+** (неделя+) → планка минимальная (15 мин)

### ⏰ Дескалация в течение дня (от текущей планки):
- **Первое предложение:** 100% текущей планки
- **Второе предложение:** 50% текущей планки
- **Третье предложение:** фиксированный минимум (15 мин)
- **Финальное предложение:** план-минимум (2-3 мин)

### 🔮 Будущая динамическая система:
- **Рост планки:** успешная работа 3+ дня → +10% к планке
- **Снижение планки:** пропуски постепенно снижают планку
- **Анализ паттернов:** учет долгосрочной истории работы
- **Персонализация:** адаптация под индивидуальные особенности

**Комментарий:** Сейчас используем хардкод значений для базовой функциональности. В будущем планка будет рассчитываться динамически на основе анализа паттернов работы пользователя.

---

### **ЭТАП 6: Заглушки для системы барьеров** 🔧
**Что делаем:** Простые текстовые ответы на барьеры, пометки для будущего

**Заглушки:**
- **Неясность задач** → "Проясни - это тоже интервал!" → Интервал планирования
- **Лень** → "План минимум спасет streak" → Предложение 2-3 минут
- **Нет времени** → "2 минуты всегда найдутся" → План минимум
- **Проект не важен** → "Тогда закрой честно" → Опция закрытия проекта

**🔮 Пометки для будущего (Задача 4 - Барьеры):**
- Многоуровневая интерактивная система (4-10 уровней вопросов)
- Глубокая проработка каждого барьера
- Система помощи с планированием и прояснением
- Анализ причин прокрастинации

---

### **ЭТАП 7: Вводный инструктаж** 🎓
**Что делаем:** В самом конце, когда все тексты утверждены

**Содержание:**
- Философия раннего начала
- Принцип "план минимум"  
- Объяснение системы эскалации
- Важность регулярности

Да, хорошо, еще вот у нас в вот эту вот систему Early Engagement Task надо добавить информацию в одну о том, почему важно начинать рано над фокусным проектом, то есть мы будем обучать и говорить, что согласно там, ну кто начинает день с важного проекта, они у них прям вот, как бы если брать один показатель, который самый важный, то первое это вот стоит это раннее начало, то есть начинать день именно с того, иначе просто ты возьмешь там одно дело, увлекся, потом другое и с каждой минуты вероятность того, что вы начнете, она снижается, снижается, снижается все больше и больше и больше, а потом ты думаешь, ну ладно, окей завтра и тут ты уже себя ощущаешь как тем, кто не контролирует работу и все это с каждым часом все хуже и хуже и хуже, поэтому лучше это в утро проснулись, сразу начали и все и поддерживаем, это прям самый важный навык, который нужно освоить.

---

## 🔄 СВЯЗЬ С ДРУГИМИ ЗАДАЧАМИ

### **Следующая задача: Адаптивные интервалы**
- Автоматический расчет оптимальной длительности
- Анализ паттернов работы
- Персонализация под пользователя

### **Задача 4: Система барьеров**
- Глубокая проработка причин прокрастинации
- Многоуровневая помощь с планированием
- Интерактивная система преодоления барьеров

---

## 📝 ТЕХНИЧЕСКИЕ ЗАМЕТКИ

### **Количество напоминаний в день:**
Зависит от дней без работы:
- 0 дней → 2-3 напоминания
- 1-2 дня → 3-4 напоминания  
- 3+ дней → 4-5 напоминаний
- Неделя+ → до полноэкранной эскалации

### **Принцип формирования сообщений:**
1. Анализ дней без работы → выбор типа первого сообщения
2. Отслеживание ответов → дескалация интервалов
3. Проверка времени → повторные предложения
4. Финальная эскалация → полноэкранное сообщение

### **Интеграция с существующими системами:**
- ProjectManager (приоритетный проект)
- MotivationManager (мотивационные сообщения)
- UnifiedReminderSystem (система напоминаний)
- WorkPatternAnalyzer (анализ паттернов)

---

## ✅ КРИТЕРИИ ГОТОВНОСТИ

- [ ] Детектор пробуждения работает
- [ ] Аналитика дней без работы ведется
- [ ] Система сообщений с двойной эскалацией реализована
- [ ] План минимум интегрирован
- [ ] Адаптивные интервалы работают (хардкод)
- [ ] Заглушки для барьеров готовы
- [ ] Вводный инструктаж создан

**Готовность к следующей задаче:** Когда пользователь регулярно получает персонализированные предложения начать работу и система адаптируется под его поведение.
